CREATE TABLE public.EmailInviteBlocks (
  Id integer NOT NULL DEFAULT nextval('"EmailInviteBlocks_Id_seq"'::regclass),
  Email text NOT NULL,
  OrganisationId integer NOT NULL,
  Reason text,
  BlockedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT EmailInviteBlocks_pkey PRIMARY KEY (Id),
  CONSTRAINT FK_EmailInviteBlocks_Org FOREIGN KEY (OrganisationId) REFERENCES public.Organisation(Id)
);
CREATE TABLE public.InviteBlocks (
  Id integer NOT NULL DEFAULT nextval('"InviteBlocks_Id_seq"'::regclass),
  UserId integer NOT NULL,
  OrganisationId integer NOT NULL,
  Reason text,
  CreatedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT InviteBlocks_pkey PRIMARY KEY (Id),
  CONSTRAINT FK_InviteBlocks_User FOREIGN KEY (UserId) REFERENCES public.Users(Id),
  CONSTRAINT FK_InviteBlocks_Org FOREIGN KEY (OrganisationId) REFERENCES public.Organisation(Id)
);
CREATE TABLE public.Invites (
  Id integer NOT NULL DEFAULT nextval('"Invites_Id_seq"'::regclass),
  Email text NOT NULL,
  InviteKey text UNIQUE,
  InviteType integer NOT NULL,
  OrganisationId integer NOT NULL,
  InviteMetadata json,
  InviteStatus integer NOT NULL DEFAULT 0,
  RejectionReason text,
  InviteExpireDate timestamp with time zone NOT NULL,
  CreatedBy integer NOT NULL,
  CreatedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT Invites_pkey PRIMARY KEY (Id),
  CONSTRAINT FK_Invites_Organisation_OrganisationId FOREIGN KEY (OrganisationId) REFERENCES public.Organisation(Id),
  CONSTRAINT FK_Invites_Users_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES public.Users(Id)
);
CREATE TABLE public.ModuleToOrganisationBridge (
  Id integer NOT NULL DEFAULT nextval('"ModuleToOrganisationBridge_Id_seq"'::regclass),
  ModuleId integer NOT NULL,
  OrganisationId integer NOT NULL,
  CONSTRAINT ModuleToOrganisationBridge_pkey PRIMARY KEY (Id),
  CONSTRAINT fk_mob_organisation FOREIGN KEY (OrganisationId) REFERENCES public.Organisation(Id),
  CONSTRAINT fk_mob_module FOREIGN KEY (ModuleId) REFERENCES public.Modules(Id)
);
CREATE TABLE public.Modules (
  Id integer NOT NULL DEFAULT nextval('"Modules_Id_seq"'::regclass),
  Name text NOT NULL,
  Description text NOT NULL,
  Attributes json NOT NULL,
  CreatedBy text NOT NULL,
  CreatedAt timestamp with time zone NOT NULL DEFAULT now(),
  ModifiedBy text NOT NULL,
  ModifiedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT Modules_pkey PRIMARY KEY (Id)
);
CREATE TABLE public.Organisation (
  Id integer NOT NULL DEFAULT nextval('"Organisation_Id_seq"'::regclass),
  Name text NOT NULL,
  OwnerId integer NOT NULL,
  Status integer NOT NULL DEFAULT 0,
  CreatedBy integer NOT NULL,
  CreatedAt timestamp with time zone NOT NULL DEFAULT now(),
  ModifiedBy integer NOT NULL,
  ModifiedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT Organisation_pkey PRIMARY KEY (Id),
  CONSTRAINT FK_Organisation_Owner FOREIGN KEY (OwnerId) REFERENCES public.Users(Id),
  CONSTRAINT FK_Organisation_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES public.Users(Id),
  CONSTRAINT FK_Organisation_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES public.Users(Id)
);
CREATE TABLE public.PermissionToRoleBridge (
  Id integer NOT NULL DEFAULT nextval('"PermissionToRoleBridge_Id_seq"'::regclass),
  RoleId integer NOT NULL,
  PermissionId integer NOT NULL,
  AssignedBy integer NOT NULL,
  AssignedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT PermissionToRoleBridge_pkey PRIMARY KEY (Id),
  CONSTRAINT fk_bridge_permission FOREIGN KEY (PermissionId) REFERENCES public.Permissions(Id),
  CONSTRAINT fk_bridge_user FOREIGN KEY (AssignedBy) REFERENCES public.Users(Id),
  CONSTRAINT fk_bridge_role FOREIGN KEY (RoleId) REFERENCES public.Roles(Id)
);
CREATE TABLE public.Permissions (
  Id integer NOT NULL DEFAULT nextval('"Permissions_Id_seq"'::regclass),
  PermissionName character varying NOT NULL UNIQUE,
  PermissionDescription text,
  Scope integer NOT NULL,
  Domain integer NOT NULL,
  CONSTRAINT Permissions_pkey PRIMARY KEY (Id)
);
CREATE TABLE public.Products (
  Id integer NOT NULL DEFAULT nextval('"Products_Id_seq"'::regclass),
  PlanID text NOT NULL,
  Name text NOT NULL,
  Price text NOT NULL,
  Description text NOT NULL,
  Attributes json NOT NULL,
  CreatedBy text NOT NULL,
  CreatedAt timestamp with time zone NOT NULL DEFAULT now(),
  ModifiedBy text NOT NULL,
  ModifiedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT Products_pkey PRIMARY KEY (Id)
);
CREATE TABLE public.Roles (
  Id integer NOT NULL DEFAULT nextval('"Roles_Id_seq"'::regclass),
  RoleName character varying NOT NULL,
  RoleDescription text NOT NULL,
  OrganisationId integer,
  CreatedBy integer NOT NULL,
  CreatedAt timestamp with time zone NOT NULL DEFAULT now(),
  IsSystemRole boolean NOT NULL DEFAULT false,
  CONSTRAINT Roles_pkey PRIMARY KEY (Id),
  CONSTRAINT fk_roles_createdby FOREIGN KEY (CreatedBy) REFERENCES public.Users(Id),
  CONSTRAINT fk_roles_organisation FOREIGN KEY (OrganisationId) REFERENCES public.Organisation(Id)
);
CREATE TABLE public.Subscriptions (
  Id integer NOT NULL DEFAULT nextval('"Subscriptions_Id_seq"'::regclass),
  ProductId integer,
  OrganisationId integer NOT NULL,
  PaystackPlatfromSubscriptionId text,
  Status integer NOT NULL DEFAULT 0,
  BillingCycle integer NOT NULL,
  Price bigint NOT NULL,
  Currency text NOT NULL,
  NextPaymentDate timestamp with time zone NOT NULL,
  InitiationDate timestamp with time zone NOT NULL,
  CancellationDate timestamp with time zone,
  PaystackSupportSubscriptionId text,
  SupportProductId integer,
  CONSTRAINT Subscriptions_pkey PRIMARY KEY (Id),
  CONSTRAINT fk_product FOREIGN KEY (ProductId) REFERENCES public.Products(Id),
  CONSTRAINT fk_organisation FOREIGN KEY (OrganisationId) REFERENCES public.Organisation(Id),
  CONSTRAINT Subscriptions_SupportProductId_fkey FOREIGN KEY (SupportProductId) REFERENCES public.SupportProducts(Id)
);
CREATE TABLE public.SupportProducts (
  Id integer NOT NULL DEFAULT nextval('"SupportProducts_Id_seq"'::regclass),
  PlanID text NOT NULL,
  Name text NOT NULL,
  Price text NOT NULL,
  Description text NOT NULL,
  Attributes json NOT NULL,
  CreatedBy text NOT NULL,
  CreatedAt timestamp with time zone NOT NULL DEFAULT now(),
  ModifiedBy text NOT NULL,
  ModifiedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT SupportProducts_pkey PRIMARY KEY (Id)
);
CREATE TABLE public.UserToOrganisationBridge (
  Id integer NOT NULL DEFAULT nextval('"UserToOrganisationBridge_Id_seq"'::regclass),
  OrganisationId integer NOT NULL,
  UserId integer NOT NULL,
  Status integer NOT NULL DEFAULT 1,
  CreatedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT UserToOrganisationBridge_pkey PRIMARY KEY (Id),
  CONSTRAINT FK_UserToOrganisationBridge_Organisation FOREIGN KEY (OrganisationId) REFERENCES public.Organisation(Id),
  CONSTRAINT FK_UserToOrganisationBridge_User FOREIGN KEY (UserId) REFERENCES public.Users(Id)
);
CREATE TABLE public.UserToRoleBridge (
  Id integer NOT NULL DEFAULT nextval('"UserToRoleBridge_Id_seq"'::regclass),
  UserId integer NOT NULL,
  RoleId integer NOT NULL,
  AssignedBy integer NOT NULL,
  AssignedAt timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT UserToRoleBridge_pkey PRIMARY KEY (Id),
  CONSTRAINT fk_user_role_assigned_bu FOREIGN KEY (AssignedBy) REFERENCES public.Users(Id),
  CONSTRAINT fk_user_role_user FOREIGN KEY (UserId) REFERENCES public.Users(Id),
  CONSTRAINT fk_user_role_role FOREIGN KEY (RoleId) REFERENCES public.Roles(Id)
);
CREATE TABLE public.Users (
  Id integer NOT NULL DEFAULT nextval('"Users_Id_seq"'::regclass),
  FirstName text NOT NULL,
  LastName text NOT NULL,
  EmailAddress text NOT NULL UNIQUE,
  HashedPassword text NOT NULL,
  VerificationPin text,
  VerificationPinExpiry timestamp with time zone,
  PasswordResetToken text,
  PasswordResetTokenExpiry timestamp with time zone,
  RefreshToken text,
  RefreshTokenExpiry timestamp with time zone,
  Status integer NOT NULL DEFAULT 0,
  CreatedBy text NOT NULL DEFAULT 'SYSTEM'::text,
  CreatedAt timestamp with time zone NOT NULL DEFAULT now(),
  ModifiedBy text NOT NULL DEFAULT 'SYSTEM'::text,
  ModifiedAt timestamp with time zone NOT NULL DEFAULT now(),
  ProfilePicture text,
  CONSTRAINT Users_pkey PRIMARY KEY (Id)
);