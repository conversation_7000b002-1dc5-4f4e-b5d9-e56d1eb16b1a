/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as TermsconditionsImport } from './routes/terms&conditions'
import { Route as RefundpolicyImport } from './routes/refundpolicy'
import { Route as ProfileImport } from './routes/profile'
import { Route as ProductsImport } from './routes/products'
import { Route as PrivacypolicyImport } from './routes/privacypolicy'
import { Route as PricingImport } from './routes/pricing'
import { Route as OrganizationImport } from './routes/organization'
import { Route as BillingImport } from './routes/billing'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const TermsconditionsRoute = TermsconditionsImport.update({
  id: '/terms&conditions',
  path: '/terms&conditions',
  getParentRoute: () => rootRoute,
} as any)

const RefundpolicyRoute = RefundpolicyImport.update({
  id: '/refundpolicy',
  path: '/refundpolicy',
  getParentRoute: () => rootRoute,
} as any)

const ProfileRoute = ProfileImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => rootRoute,
} as any)

const ProductsRoute = ProductsImport.update({
  id: '/products',
  path: '/products',
  getParentRoute: () => rootRoute,
} as any)

const PrivacypolicyRoute = PrivacypolicyImport.update({
  id: '/privacypolicy',
  path: '/privacypolicy',
  getParentRoute: () => rootRoute,
} as any)

const PricingRoute = PricingImport.update({
  id: '/pricing',
  path: '/pricing',
  getParentRoute: () => rootRoute,
} as any)

const OrganizationRoute = OrganizationImport.update({
  id: '/organization',
  path: '/organization',
  getParentRoute: () => rootRoute,
} as any)

const BillingRoute = BillingImport.update({
  id: '/billing',
  path: '/billing',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/billing': {
      id: '/billing'
      path: '/billing'
      fullPath: '/billing'
      preLoaderRoute: typeof BillingImport
      parentRoute: typeof rootRoute
    }
    '/organization': {
      id: '/organization'
      path: '/organization'
      fullPath: '/organization'
      preLoaderRoute: typeof OrganizationImport
      parentRoute: typeof rootRoute
    }
    '/pricing': {
      id: '/pricing'
      path: '/pricing'
      fullPath: '/pricing'
      preLoaderRoute: typeof PricingImport
      parentRoute: typeof rootRoute
    }
    '/privacypolicy': {
      id: '/privacypolicy'
      path: '/privacypolicy'
      fullPath: '/privacypolicy'
      preLoaderRoute: typeof PrivacypolicyImport
      parentRoute: typeof rootRoute
    }
    '/products': {
      id: '/products'
      path: '/products'
      fullPath: '/products'
      preLoaderRoute: typeof ProductsImport
      parentRoute: typeof rootRoute
    }
    '/profile': {
      id: '/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof ProfileImport
      parentRoute: typeof rootRoute
    }
    '/refundpolicy': {
      id: '/refundpolicy'
      path: '/refundpolicy'
      fullPath: '/refundpolicy'
      preLoaderRoute: typeof RefundpolicyImport
      parentRoute: typeof rootRoute
    }
    '/terms&conditions': {
      id: '/terms&conditions'
      path: '/terms&conditions'
      fullPath: '/terms&conditions'
      preLoaderRoute: typeof TermsconditionsImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/billing': typeof BillingRoute
  '/organization': typeof OrganizationRoute
  '/pricing': typeof PricingRoute
  '/privacypolicy': typeof PrivacypolicyRoute
  '/products': typeof ProductsRoute
  '/profile': typeof ProfileRoute
  '/refundpolicy': typeof RefundpolicyRoute
  '/terms&conditions': typeof TermsconditionsRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/billing': typeof BillingRoute
  '/organization': typeof OrganizationRoute
  '/pricing': typeof PricingRoute
  '/privacypolicy': typeof PrivacypolicyRoute
  '/products': typeof ProductsRoute
  '/profile': typeof ProfileRoute
  '/refundpolicy': typeof RefundpolicyRoute
  '/terms&conditions': typeof TermsconditionsRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/billing': typeof BillingRoute
  '/organization': typeof OrganizationRoute
  '/pricing': typeof PricingRoute
  '/privacypolicy': typeof PrivacypolicyRoute
  '/products': typeof ProductsRoute
  '/profile': typeof ProfileRoute
  '/refundpolicy': typeof RefundpolicyRoute
  '/terms&conditions': typeof TermsconditionsRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/billing'
    | '/organization'
    | '/pricing'
    | '/privacypolicy'
    | '/products'
    | '/profile'
    | '/refundpolicy'
    | '/terms&conditions'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/billing'
    | '/organization'
    | '/pricing'
    | '/privacypolicy'
    | '/products'
    | '/profile'
    | '/refundpolicy'
    | '/terms&conditions'
  id:
    | '__root__'
    | '/'
    | '/billing'
    | '/organization'
    | '/pricing'
    | '/privacypolicy'
    | '/products'
    | '/profile'
    | '/refundpolicy'
    | '/terms&conditions'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  BillingRoute: typeof BillingRoute
  OrganizationRoute: typeof OrganizationRoute
  PricingRoute: typeof PricingRoute
  PrivacypolicyRoute: typeof PrivacypolicyRoute
  ProductsRoute: typeof ProductsRoute
  ProfileRoute: typeof ProfileRoute
  RefundpolicyRoute: typeof RefundpolicyRoute
  TermsconditionsRoute: typeof TermsconditionsRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  BillingRoute: BillingRoute,
  OrganizationRoute: OrganizationRoute,
  PricingRoute: PricingRoute,
  PrivacypolicyRoute: PrivacypolicyRoute,
  ProductsRoute: ProductsRoute,
  ProfileRoute: ProfileRoute,
  RefundpolicyRoute: RefundpolicyRoute,
  TermsconditionsRoute: TermsconditionsRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/billing",
        "/organization",
        "/pricing",
        "/privacypolicy",
        "/products",
        "/profile",
        "/refundpolicy",
        "/terms&conditions"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/billing": {
      "filePath": "billing.tsx"
    },
    "/organization": {
      "filePath": "organization.tsx"
    },
    "/pricing": {
      "filePath": "pricing.tsx"
    },
    "/privacypolicy": {
      "filePath": "privacypolicy.tsx"
    },
    "/products": {
      "filePath": "products.tsx"
    },
    "/profile": {
      "filePath": "profile.tsx"
    },
    "/refundpolicy": {
      "filePath": "refundpolicy.tsx"
    },
    "/terms&conditions": {
      "filePath": "terms&conditions.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
