// API Configuration
const API_ADMIN_URL = 'https://api.admin.local.ssf:8443/api'; // local
// const API_ADMIN_URL = 'https://api.admin.smartsensorflow.io/api' // UAT
const API_IDENTIFY_URL = 'https://api.auth.local.ssf:8443/api'; // local
// const API_IDENTIFY_URL = ' https://api.identity.smartsensorflow.io/api'; // UAT

export interface ValidationError {
  path: string;
  message: string;
}

export interface ApiError {
  title: string;
  status: number;
  detail?: string;
  instance?: string;
  extensions?: {
    validationIssues?: ValidationError[];
  };
  errors?: Record<string, string[]>;
  validationErrors?: ValidationError[];
}

export enum OrganisationState {
  Inactive = 0,
  Active = 1,
  Disabled = 2
}

export enum SubscriptionStatus {
  Active = "Active",
  Inactive = "Inactive",
  Cancelling = "Cancelling",
  Cancelled = "Cancelled"
}

export enum BillingCycle {
  Monthly = "Monthly",
  Yearly = "Yearly"
}

export enum CancellationType {
  Immediate = "Immediate",
  NextBillingPeriod = "NextBillingPeriod"
}

export enum PaystackSubscriptionType {
  Platform = "Platform",
  Support = "Support"
}

// Payment interfaces
export interface InitializeTransactionRequest {
  productId: number;
  organizationId: number;
}

export interface InitializeTransactionResponse {
  authorizationUrl: string;
}

export interface VerifyTransactionResponse {
  status: string;
  reference: string;
  amount: number;
  currency: string;
  customerEmail: string;
  planCode: string;
  subscriptionCode: string;
  metadata?: any;
}



export enum ProductType {
  Platform = "Platform",
  Support = "Support"
}

export interface ProductAttributesPlatform {
  gateways: string;
  assets: string;
  dataPoints: string;
  support: boolean;
  whiteLabel: boolean;
}

export interface ProductAttributesSupport {
  supportChannels: string[];
  responseSLA: string;
  knowledgeBaseAccess: boolean;
  technicalConsultation: string;
  proactiveMonitoring: boolean;
  businessReviews: boolean;
  onDemandTraining: boolean;
  dedicatedAccountManager: boolean;
}

export interface ProductResponse {
  id: number;
  planID: string;
  name: string;
  price: string;
  type: ProductType;
  description: string;
  attributes: ProductAttributesPlatform | ProductAttributesSupport;
}

export interface GetProductsResponse {
  products: ProductResponse[];
}

export interface SubscriptionResponse {
  id: number;
  productName: string;
  status: SubscriptionStatus;
  billingCycle: BillingCycle;
  price: number;
  currency: string;
  nextPaymentDate: string;
  initiationDate: string;
  cancellationDate?: string;
  // Paddle legacy field
  paddleSubscriptionId?: string;
  // Paystack fields
  paystackPlatformSubscriptionId?: string;
  paystackSupportSubscriptionId?: string;
  emailToken?: string;
}

export interface GetSubscriptionResponse {
  subscriptions: SubscriptionResponse[];
}

export interface ModuleAttributes {
  urlLive: string;
  urlLocal: string;
}

export interface ModuleResponse {
  id: string;
  name: string;
  description: string;
  attributes: ModuleAttributes;
  createdBy: string;
  createdAt: string;
  modifiedBy: string;
  modifiedAt: string;
}

export interface GetModulesResponse {
  modules: ModuleResponse[];
}

export interface OrganisationResponse {
  id: number;
  name: string;
  organisationStatus: OrganisationState;
  createdAt: string;
  modules: ModuleResponse[];
  isOwner: boolean;
  userStatus: AccountStatus;
}

export interface GetOrganisationResponse {
  organisations: OrganisationResponse[];
}

export interface CreateOrganisationResponse {
  organisationId: number;
}

export enum AccountStatus {
  Pending = 0,
  Active = 1,
  Suspended = 2,
  Removed = 3
}

export interface UserResponse {
  id: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
  profilePicture?: string;
  userStatus: AccountStatus;
  organisationStatus: AccountStatus;
  isOwner: boolean;
}

export interface GetUsersResponse {
  users: UserResponse[];
}

export interface InviteUserResponse {
  success: boolean;
  failureReason?: string;
}

export interface PendingInviteResponse {
  inviteKey: string;
  organisationName?: string;
  organisationId?: number;
  inviteExpireDate?: string;
  createdAt?: string;
  firstName?: string;
  lastName?: string;
}

export interface GetPendingInvitesResponse {
  invites: PendingInviteResponse[];
}

// Response from /me endpoint that includes both user info and invites
export interface MeResponse {
  firstName: string;
  lastName: string;
  emailAddress: string;
  profilePicture?: string;
  invites: PendingInviteResponse[];
}

export interface PermissionResponse {
  id: number;
  permissionName: string;
  permissionDescription: string;
  scope: number;
  domain: number;
}

export interface PermissionsResponse {
  permissions: PermissionResponse[];
}

export interface RoleResponse {
  id: number;
  roleName: string;
  roleDescription: string;
  organisationId: number;
  createdBy: string;
  createdAt: number;
}

export interface RolesResponse {
  roles: RoleResponse[];
}

export interface CreateRoleRequest {
  roleName: string;
  roleDescription: string;
  organisationId: number;
}

export interface UserRoleResponse {
  userId: number;
  roleId: number;
  roleName: string;
}

export interface UserRolesResponse {
  userRoles: UserRoleResponse[];
}

// API Service Class
class ApiService {
  private async makeRequest<T>(
    baseUrl: string,
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${baseUrl}${endpoint}`;

    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        const contentType = response.headers.get('content-type');
        console.log('Error response content-type:', contentType);
        console.log('Response status:', response.status, response.statusText);

        if (contentType?.includes('application/json')) {
          const errorData: ApiError = await response.json();
          console.log('Error data received:', errorData);

          // Handle ProblemDetails validation errors (backend format)
          if (errorData.extensions?.validationIssues && errorData.extensions.validationIssues.length > 0) {
            const firstError = errorData.extensions.validationIssues[0];
            console.log('Using validation issue:', firstError.message);
            throw new Error(firstError.message);
          }

          // Handle legacy validation errors format (if any)
          if (errorData.validationErrors && errorData.validationErrors.length > 0) {
            const firstError = errorData.validationErrors[0];
            console.log('Using legacy validation error:', firstError.message);
            throw new Error(firstError.message);
          }

          // Handle general errors
          if (errorData.errors) {
            const firstErrorKey = Object.keys(errorData.errors)[0];
            if (firstErrorKey && errorData.errors[firstErrorKey].length > 0) {
              console.log('Using general error:', errorData.errors[firstErrorKey][0]);
              throw new Error(errorData.errors[firstErrorKey][0]);
            }
          }

          // Use detail first (more specific), then title, then fallback
          const errorMessage = errorData.detail || errorData.title || `HTTP ${response.status}`;
          console.log('Using fallback error:', errorMessage);
          throw new Error(errorMessage);
        } else {
          console.log('Non-JSON response, using status text');
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      // Handle empty responses (like 200 OK with no content)
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return {} as T;
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('An unexpected error occurred');
    }
  }

  async refreshToken(): Promise<{ success: boolean; requiresLogin: boolean }> {
    const url = `${API_IDENTIFY_URL}/user/refresh`;

    const defaultOptions: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    };

    try {
      const response = await fetch(url, defaultOptions);

      if (response.ok) {
        // 200 OK - Token refreshed successfully
        console.log('Token refreshed successfully');
        return { success: true, requiresLogin: false };
      } else if (response.status === 401) {
        // 401 Unauthorized - Invalid/expired refresh token
        // Backend has already cleared the cookies
        console.log('Refresh token invalid or expired, login required');
        return { success: false, requiresLogin: true };
      } else {
        // Other error status codes
        const contentType = response.headers.get('content-type');
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        if (contentType?.includes('application/json')) {
          try {
            const errorData: ApiError = await response.json();
            errorMessage = errorData.detail || errorData.title || errorMessage;
          } catch {
            // If JSON parsing fails, use the default error message
          }
        }

        console.error('Token refresh failed:', errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('An unexpected error occurred during token refresh');
    }
  }

  async logout(): Promise<void> {
    return this.makeRequest<void>(API_IDENTIFY_URL, '/user/logout', {
      method: 'POST',
    });
  }

  // User Info
  async getCurrentUser(): Promise<MeResponse> {
    return this.makeRequest<MeResponse>(
      API_IDENTIFY_URL,
      '/me',
      {
        method: 'GET',
      }
    );
  }

  // Organization Management
  async getOrganisations(): Promise<GetOrganisationResponse> {
    return this.makeRequest<GetOrganisationResponse>(API_ADMIN_URL, '/organisation', {
      method: 'GET',
    });
  }

  async createOrganisation(name: string): Promise<CreateOrganisationResponse> {
    return this.makeRequest<CreateOrganisationResponse>(API_ADMIN_URL, '/organisation/create', {
      method: 'POST',
      body: JSON.stringify({ name }),
    });
  }

  // Product Management
  async getProducts(): Promise<GetProductsResponse> {
    return this.makeRequest<GetProductsResponse>(API_ADMIN_URL, '/billing/products', {
      method: 'GET',
    });
  }

  // Profile Management
  async updateProfile(data: { firstName: string; lastName: string; emailAddress: string }): Promise<void> {
    return this.makeRequest<void>(
      API_IDENTIFY_URL,
      '/profile/update',
      {
        method: 'PUT',
        body: JSON.stringify(data),
      }
    );
  }

  async updateProfilePicture(profilePictureBase64: string): Promise<void> {
    return this.makeRequest<void>(
      API_IDENTIFY_URL,
      '/profile/update-profile-picture',
      {
        method: 'POST',
        body: JSON.stringify({ profilePictureBase64 }),
      }
    );
  }

  async removeProfilePicture(): Promise<void> {
    // Remove profile picture by sending empty base64 string
    return this.makeRequest<void>(
      API_IDENTIFY_URL,
      '/profile/update-profile-picture',
      {
        method: 'POST',
        body: JSON.stringify({ profilePictureBase64: '' }),
      }
    );
  }

  // Subscription Management
  async getSubscriptions(organisationId: number): Promise<GetSubscriptionResponse> {
    return this.makeRequest<GetSubscriptionResponse>(API_ADMIN_URL, `/billing/subscription/${organisationId}`, {
      method: 'GET',
    });
  }

  async cancelSubscription(subscriptionId: number, cancellationType: CancellationType): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, '/billing/subscription/cancel', {
      method: 'POST',
      body: JSON.stringify({ subscriptionId, cancellationType }),
    });
  }

  // Payment Transaction Management
  async initializeTransaction(request: InitializeTransactionRequest): Promise<InitializeTransactionResponse> {
    return this.makeRequest<InitializeTransactionResponse>(API_ADMIN_URL, '/billing/transaction/initialize', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async verifyPlatformTransaction(reference: string): Promise<VerifyTransactionResponse> {
    return this.makeRequest<VerifyTransactionResponse>(API_ADMIN_URL, `/billing/transaction/verify/platform?reference=${reference}`, {
      method: 'GET',
    });
  }

  async cancelPlatformSubscription(subscriptionCode: string, emailToken: string): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, '/billing/paystack/cancel/platform', {
      method: 'POST',
      body: JSON.stringify({ subscriptionCode, emailToken }),
    });
  }



  async verifySupportTransaction(reference: string): Promise<VerifyTransactionResponse> {
    return this.makeRequest<VerifyTransactionResponse>(API_ADMIN_URL, `/billing/transaction/verify/support?reference=${reference}`, {
      method: 'GET',
    });
  }

  async cancelSupportSubscription(subscriptionCode: string, emailToken: string): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, '/billing/paystack/cancel/support', {
      method: 'POST',
      body: JSON.stringify({ subscriptionCode, emailToken }),
    });
  }



  // Module Management
  async getModules(): Promise<GetModulesResponse> {
    return this.makeRequest<GetModulesResponse>(API_ADMIN_URL, '/modules', {
      method: 'GET',
    });
  }

  async addModuleToOrganisation(organisationId: number, moduleId: number): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, '/organisation/modules/add', {
      method: 'POST',
      body: JSON.stringify({ organisationId, moduleId }),
    });
  }

  // Organisation User Management
  async getOrganisationUsers(organisationId: number): Promise<GetUsersResponse> {
    return this.makeRequest<GetUsersResponse>(API_ADMIN_URL, `/user/organisation/${organisationId}`, {
      method: 'GET',
    });
  }

  async inviteUserToOrganisation(
    organisationId: number,
    emailAddress: string,
    firstName: string,
    surname: string
  ): Promise<InviteUserResponse> {
    return this.makeRequest<InviteUserResponse>(API_ADMIN_URL, `/invite/organisation/${organisationId}`, {
      method: 'POST',
      body: JSON.stringify({ emailAddress, firstName, surname }),
    });
  }

  async removeUserFromOrganisation(organisationId: number, userId: number): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, `/user/organisation/${organisationId}/remove`, {
      method: 'POST',
      body: JSON.stringify({ userId }),
    });
  }

  async suspendUserInOrganisation(organisationId: number, userId: number): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, `/user/organisation/${organisationId}/suspend`, {
      method: 'POST',
      body: JSON.stringify({ userId }),
    });
  }

  async activateUserInOrganisation(organisationId: number, userId: number): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, `/user/organisation/${organisationId}/activate`, {
      method: 'POST',
      body: JSON.stringify({ userId }),
    });
  }



  async acceptOrganisationInvite(inviteKey: string): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, '/invite/accept', {
      method: 'POST',
      body: JSON.stringify({ inviteKey }),
    });
  }

  async rejectOrganisationInvite(
    inviteKey: string,
    rejectReason?: string,
    isBlocked: boolean = false,
    blockReason?: string
  ): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, '/invite/reject', {
      method: 'POST',
      body: JSON.stringify({
        inviteKey,
        rejectReason,
        isBlocked,
        blockReason
      }),
    });
  }

  async getPendingInvites(): Promise<GetPendingInvitesResponse> {
    try {
      // Get invites from the /me endpoint since that's where they're actually provided
      const meResponse = await this.getCurrentUser();
      return { invites: meResponse.invites || [] };
    } catch (error) {
      console.warn('Failed to fetch pending invites from /me endpoint:', error);
      return { invites: [] };
    }
  }

  // Permissions Management
  async getAllPermissions(): Promise<PermissionsResponse> {
    return this.makeRequest<PermissionsResponse>(API_ADMIN_URL, '/permissions', {
      method: 'GET',
    });
  }

  // Role Management
  async createRole(request: CreateRoleRequest): Promise<void> {
    console.log('Creating role with request:', request);
    console.log(`Making createRole request to: ${API_ADMIN_URL}/role/create`);

    const result = await this.makeRequest<void>(API_ADMIN_URL, '/role/create', {
      method: 'POST',
      body: JSON.stringify(request),
    });

    console.log('createRole successful:', result);
    return result;
  }

  async getRoles(organisationId: number): Promise<RolesResponse> {
    try {
      console.log(`Making getRoles request to: ${API_ADMIN_URL}/role/organisation/${organisationId}`);

      // Try GET method first (like GetOrganisationUsers)
      try {
        const result = await this.makeRequest<RolesResponse>(API_ADMIN_URL, `/role/organisation/${organisationId}`, {
          method: 'GET',
        });
        console.log('getRoles successful with GET:', result);
        return result;
      } catch (getError) {
        console.log('GET failed, trying POST:', getError);

        // Fallback to POST with empty body
        const result = await this.makeRequest<RolesResponse>(API_ADMIN_URL, `/role/organisation/${organisationId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({}),
        });
        console.log('getRoles successful with POST:', result);
        return result;
      }
    } catch (error) {
      console.error('getRoles API call failed:', error);
      // Return empty response if endpoint doesn't exist
      if (error instanceof Error && error.message.includes('404')) {
        console.warn('Roles endpoint not found, returning empty roles list');
        return { roles: [] };
      }
      throw error;
    }
  }

  // TODO: Backend implementation needed
  // async deleteRole(roleId: number): Promise<void> {
  //   throw new Error('Delete role endpoint not implemented in backend');
  // }

  // Role to User Assignment
  async assignRoleToUser(userId: number, roleId: number): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, '/role/user/add', {
      method: 'POST',
      body: JSON.stringify({ usersId: userId, roleId }),
    });
  }

  // TODO: Backend implementation needed
  // async removeRoleFromUser(userId: number, roleId: number, organisationId: number): Promise<void> {
  //   throw new Error('Remove role from user endpoint not implemented in backend');
  // }

  // TODO: Backend implementation needed
  // async getUserRoles(userId: number, organisationId: number): Promise<UserRolesResponse> {
  //   throw new Error('Get user roles endpoint not implemented in backend');
  // }

  // Permission to Role Assignment
  async addPermissionToRole(roleId: number, permissionId: number): Promise<void> {
    return this.makeRequest<void>(API_ADMIN_URL, '/role/permission/add', {
      method: 'POST',
      body: JSON.stringify({ roleId, permissionId }),
    });
  }

  // TODO: Backend implementation needed
  // async removePermissionFromRole(roleId: number, permissionId: number): Promise<void> {
  //   throw new Error('Remove permission from role endpoint not implemented in backend');
  // }

  // TODO: Backend implementation needed - need GET /api/role/{roleId}/permissions
  // async getRolePermissions(roleId: number): Promise<PermissionsResponse> {
  //   throw new Error('Get role permissions endpoint not implemented in backend');
  // }

  // Root endpoint
  async getRoot(): Promise<{ message: string }> {
    return this.makeRequest<{ message: string }>(API_ADMIN_URL.replace('/api', ''), '/', {
      method: 'GET',
    });
  }

  // Utility method to make authenticated requests with automatic token refresh
  async makeAuthenticatedRequest<T>(
    baseUrl: string,
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    try {
      // First, try the request
      return await this.makeRequest<T>(baseUrl, endpoint, options);
    } catch (error) {
      // If we get a 401, try to refresh the token and retry once
      if (error instanceof Error && error.message.includes('401')) {
        console.log('Got 401, attempting token refresh...');
        const refreshResult = await this.refreshToken();

        if (refreshResult.success) {
          console.log('Token refreshed, retrying request...');
          // Retry the original request
          return await this.makeRequest<T>(baseUrl, endpoint, options);
        } else if (refreshResult.requiresLogin) {
          // Refresh failed, user needs to login
          throw new Error('Authentication required - please login');
        }
      }

      // Re-throw the original error if it's not a 401 or refresh failed
      throw error;
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();