import { FC, useState } from "react";
import { useOrganisationStore } from "../../../lib/stores/organisation.store";
import { useUserStore } from "../../../lib/stores/user.store";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../ui/card";
import { Button } from "../../ui/button";
import { Badge } from "../../ui/badge";
import { Separator } from "../../ui/separator";
import {
  CreditCard,
  Calendar,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Building,
  User,
  RefreshCw,
} from "lucide-react";
import {
  SubscriptionResponse,
  SubscriptionStatus,
  BillingCycle,
  CancellationType,
  apiService,
} from "../../../lib/api.service";
import { toast } from "sonner";
import Loader from "../../ui/loader";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../../ui/alert-dialog";

const BillingComponent: FC = () => {
  const { organisations, selectedOrganisation, refreshSelectedOrganisation } =
    useOrganisationStore();
  const { firstName, lastName, emailAddress } = useUserStore();

  const [isLoading, setIsLoading] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<
    | (SubscriptionResponse & {
        organisationId: number;
        organisationName: string;
      })
    | null
  >(null);
  const [isAlertDialogOpen, setIsAlertDialogOpen] = useState(false);

  // Get subscriptions only from organizations the user owns
  const ownedOrganisations = organisations.filter((org) => org.isOwner);
  const allSubscriptions = ownedOrganisations.flatMap((org) =>
    org.subscriptions.map((sub) => ({
      ...sub,
      organisationName: org.name,
      organisationId: org.id,
    }))
  );

  const activeSubscriptions = allSubscriptions.filter(
    (sub) => sub.status === SubscriptionStatus.Active
  );
  const inactiveSubscriptions = allSubscriptions.filter(
    (sub) => sub.status !== SubscriptionStatus.Active
  );

  const handleRefreshBilling = async () => {
    setIsLoading(true);
    try {
      if (selectedOrganisation) {
        await refreshSelectedOrganisation();
      }
      toast.success("Billing information refreshed");
    } catch (error) {
      toast.error("Failed to refresh billing information");
      console.error("Refresh billing error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenCancelDialog = (
    subscription: SubscriptionResponse & {
      organisationId: number;
      organisationName: string;
    }
  ) => {
    setSelectedSubscription(subscription);
    setIsAlertDialogOpen(true);
  };

  const handleCancelSubscription = async (
    cancellationType: CancellationType
  ) => {
    if (!selectedSubscription) return;

    setIsCancelling(true);
    try {
      await apiService.cancelSubscription(
        selectedSubscription.id,
        cancellationType
      );
      toast.success(
        `Subscription ${cancellationType === CancellationType.Immediate ? "cancelled immediately" : "scheduled for cancellation"}`
      );
      await handleRefreshBilling();
    } catch (error) {
      toast.error("Failed to cancel subscription");
      console.error("Cancel subscription error:", error);
    } finally {
      setIsCancelling(false);
      setSelectedSubscription(null);
      setIsAlertDialogOpen(false);
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency || "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusBadge = (status: SubscriptionStatus) => {
    switch (status) {
      case SubscriptionStatus.Active:
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        );
      case SubscriptionStatus.Cancelling:
        return (
          <Badge variant="destructive">
            <Clock className="w-3 h-3 mr-1" />
            Cancelling
          </Badge>
        );
      case SubscriptionStatus.Cancelled:
        return (
          <Badge variant="secondary">
            <XCircle className="w-3 h-3 mr-1" />
            Cancelled
          </Badge>
        );
      case SubscriptionStatus.Inactive:
        return (
          <Badge variant="outline">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Inactive
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getBillingCycleBadge = (cycle: BillingCycle) => {
    return (
      <Badge variant="outline">
        <Calendar className="w-3 h-3 mr-1" />
        {cycle}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Billing & Subscriptions
          </h1>
          <p className="text-muted-foreground">
            Manage your subscriptions and billing information
          </p>
        </div>
        <Button
          onClick={handleRefreshBilling}
          disabled={isLoading}
          variant="outline"
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      {/* Account Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Subscriptions
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {activeSubscriptions.length}
            </div>
            <p className="text-xs text-muted-foreground">Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Organizations
            </CardTitle>
            <Building className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {ownedOrganisations.length}
            </div>
            <p className="text-xs text-muted-foreground">Organizations owned</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Spend</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                activeSubscriptions
                  .filter((sub) => sub.billingCycle === BillingCycle.Monthly)
                  .reduce((total, sub) => total + sub.price, 0),
                "USD"
              )}
            </div>
            <p className="text-xs text-muted-foreground">Per month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Account Status
            </CardTitle>
            <User className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
            <p className="text-xs text-muted-foreground">
              {firstName} {lastName}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* No owned organizations message */}
      {ownedOrganisations.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Building className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              No Organizations Owned
            </h3>
            <p className="text-muted-foreground mb-4">
              You don't own any organizations. Only organization owners can view
              billing information.
            </p>
            <p className="text-sm text-muted-foreground">
              If you're a member of an organization, contact the owner for
              billing details.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Active Subscriptions */}
      {ownedOrganisations.length > 0 && activeSubscriptions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Active Subscriptions
            </CardTitle>
            <CardDescription>
              Your currently active subscription plans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activeSubscriptions.map((subscription) => (
                <div
                  key={`${subscription.organisationId}-${subscription.id}`}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold">
                        {subscription.productName}
                      </h3>
                      {getStatusBadge(subscription.status)}
                      {getBillingCycleBadge(subscription.billingCycle)}
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p className="flex items-center gap-2">
                        <Building className="w-4 h-4" />
                        {subscription.organisationName}
                      </p>
                      <p className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Next payment: {formatDate(subscription.nextPaymentDate)}
                      </p>
                      <p className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4" />
                        {formatCurrency(
                          subscription.price,
                          subscription.currency
                        )}{" "}
                        / {subscription.billingCycle.toLowerCase()}
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOpenCancelDialog(subscription)}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Inactive/Cancelled Subscriptions */}
      {ownedOrganisations.length > 0 && inactiveSubscriptions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-gray-600" />
              Inactive & Cancelled Subscriptions
            </CardTitle>
            <CardDescription>
              Your previous and cancelled subscription plans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {inactiveSubscriptions.map((subscription) => (
                <div
                  key={`${subscription.organisationId}-${subscription.id}`}
                  className="flex items-center justify-between p-4 border rounded-lg bg-muted/30"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-muted-foreground">
                        {subscription.productName}
                      </h3>
                      {getStatusBadge(subscription.status)}
                      {getBillingCycleBadge(subscription.billingCycle)}
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p className="flex items-center gap-2">
                        <Building className="w-4 h-4" />
                        {subscription.organisationName}
                      </p>
                      <p className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Started: {formatDate(subscription.initiationDate)}
                      </p>
                      {subscription.cancellationDate && (
                        <p className="flex items-center gap-2">
                          <XCircle className="w-4 h-4" />
                          Cancelled: {formatDate(subscription.cancellationDate)}
                        </p>
                      )}
                      <p className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4" />
                        {formatCurrency(
                          subscription.price,
                          subscription.currency
                        )}{" "}
                        / {subscription.billingCycle.toLowerCase()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Subscriptions */}
      {allSubscriptions.length === 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              No Subscriptions
            </CardTitle>
            <CardDescription>
              You don't have any subscriptions yet
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center py-8">
            <div className="space-y-4">
              <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                <CreditCard className="h-8 w-8 text-muted-foreground" />
              </div>
              <div>
                <h3 className="font-semibold">
                  Get Started with a Subscription
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Create an organization and choose a plan to get started
                </p>
              </div>
              <Button>
                <Building className="h-4 w-4 mr-2" />
                Create Organization
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Billing Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Billing Information
          </CardTitle>
          <CardDescription>Your account and billing details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">Account Holder</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <p>
                  {firstName} {lastName}
                </p>
                <p>{emailAddress}</p>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Payment Method</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <p>Managed by Paddle</p>
                <p className="text-xs">
                  Payment methods are managed through our secure payment
                  processor
                </p>
              </div>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Need Help?</h4>
              <p className="text-sm text-muted-foreground">
                Contact our support team for billing assistance
              </p>
            </div>
            <Button variant="outline">Contact Support</Button>
          </div>
        </CardContent>
      </Card>

      {/* Centralized Cancel Subscription Dialog */}
      <AlertDialog open={isAlertDialogOpen} onOpenChange={setIsAlertDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Subscription</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedSubscription && (
                <>
                  Are you sure you want to cancel your{" "}
                  <strong>{selectedSubscription.productName}</strong>{" "}
                  subscription for{" "}
                  <strong>{selectedSubscription.organisationName}</strong>?
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isCancelling}>
              Keep Subscription
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                handleCancelSubscription(CancellationType.NextBillingPeriod)
              }
              disabled={isCancelling}
            >
              {isCancelling ? (
                <Loader text="Cancelling..." size="sm" />
              ) : (
                "Cancel at Period End"
              )}
            </AlertDialogAction>
            <AlertDialogAction
              onClick={() =>
                handleCancelSubscription(CancellationType.Immediate)
              }
              disabled={isCancelling}
              className="bg-red-600 hover:bg-red-700"
            >
              {isCancelling ? (
                <Loader text="Cancelling..." size="sm" />
              ) : (
                "Cancel Immediately"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default BillingComponent;
