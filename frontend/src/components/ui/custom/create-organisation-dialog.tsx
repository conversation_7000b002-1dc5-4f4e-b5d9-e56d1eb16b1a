import { <PERSON><PERSON> } from "../button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "../dialog";
import { Input } from "../input";
import { Progress } from "../progress";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
} from "../card";
import { <PERSON><PERSON>Area } from "../scroll-area";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../form";
import { BrainCircuit, ArrowRight } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useEffect, useMemo, useState } from "react";
import { useUserStore } from "../../../lib/stores/user.store";
import { useOrganisationStore } from "../../../lib/stores/organisation.store";
import {
  apiService,
  ProductResponse,
  SupportProductResponse,
} from "../../../lib/api.service";
import { toast } from "sonner";
import { Step3SupportSelection } from "./Step3SupportSelection";

interface CreateOrganisationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: (data: OrganisationFormData) => void;
  initialStep?: number;
}

interface OrganisationFormData {
  name: string;
}

export interface StepProps {
  data: OrganisationFormData;
  onDataChange: (data: Partial<OrganisationFormData>) => void;
  onNext: () => void;
  onPrevious: () => void;
  isValid: boolean;
  isFirstStep: boolean;
  isLastStep: boolean;
}

const TOTAL_STEPS = 3;

interface PaystackPaymentData {
  reference: string;
  status: string;
  amount: number;
  currency: string;
  customerEmail: string;
  planCode: string;
  subscriptionCode: string;
}

const organisationNameSchema = z.object({
  name: z
    .string()
    .min(2, "Organisation name must be at least 2 characters")
    .max(100, "Organisation name must be less than 100 characters")
    .trim(),
});

type OrganisationNameFormData = z.infer<typeof organisationNameSchema>;

function Step1OrganisationName({
  data,
  onDataChange,
  onNext,
  isValid,
  isFirstStep,
  isLastStep,
}: StepProps) {
  const form = useForm<OrganisationNameFormData>({
    resolver: zodResolver(organisationNameSchema),
    defaultValues: {
      name: data.name,
    },
  });

  const handleSubmit = async (formData: OrganisationNameFormData) => {
    onDataChange({ name: formData.name });

    // Create organization immediately after name is provided
    try {
      const organisationStore = useOrganisationStore.getState();
      const newOrganisation = await organisationStore.createOrganisation(
        formData.name
      );
      console.log("Organization created successfully:", newOrganisation);

      // Show success toast
      toast.success(`Organization "${formData.name}" created successfully!`);

      onNext();
    } catch (error) {
      console.error("Failed to create organization:", error);
      toast.error("Failed to create organization. Please try again.");
    }
  };

  useEffect(() => {
    form.setValue("name", data.name);
  }, [data.name, form]);

  useEffect(() => {
    const subscription = form.watch((value) => {
      if (value.name !== undefined) {
        onDataChange({ name: value.name });
      }
    });
    return () => subscription.unsubscribe();
  }, [form, onDataChange]);

  return (
    <div className="space-y-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Organisation Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter organisation name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {}}
              disabled={isFirstStep}
              className="invisible"
            >
              Previous
            </Button>
            <Button type="submit" disabled={!isValid}>
              {isFirstStep
                ? "Create Organisation"
                : isLastStep
                  ? "Complete Setup"
                  : "Next"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

function Step2PaymentSelection({
  onNext,
  onPrevious,
  isFirstStep,
  isLastStep,
  onPaymentStart,
  onPaymentEnd,
}: StepProps & { onPaymentStart?: () => void; onPaymentEnd?: () => void }) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [products, setProducts] = useState<ProductResponse[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [paymentReference, setPaymentReference] = useState<string | null>(null);
  const user = useUserStore((state) => state);
  const {
    selectedOrganisation,
    refreshSelectedOrganisation,
    fetchOrganisations,
  } = useOrganisationStore();

  // Fetch products from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoadingProducts(true);
        const response = await apiService.getProducts();
        setProducts(response.products);
        console.log("Products loaded:", response.products);
      } catch (error) {
        console.error("Failed to fetch products:", error);
        toast.error("Failed to load subscription plans. Please try again.");
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, []);

  // Handle payment verification when returning from Paystack
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const reference = urlParams.get("reference");

    if (reference && !paymentReference) {
      setPaymentReference(reference);
      verifyPayment(reference);
    }
  }, [paymentReference]);

  const verifyPayment = async (reference: string) => {
    try {
      setIsProcessing(true);
      const result = await apiService.verifyPlatformTransaction(reference);

      if (result.status === "success") {
        // Find the product that matches the plan code
        const selectedProduct = products.find(
          (product) => product.planID === result.planCode
        );

        console.log(
          `Payment successful for ${selectedProduct?.name || "Unknown"} plan.`
        );

        // Show success toast
        toast.success(
          `Payment successful! ${selectedProduct?.name || "Plan"} activated.`
        );

        setTimeout(async () => {
          try {
            console.log(
              "Refreshing application state after payment success (with webhook delay)..."
            );

            await fetchOrganisations();

            if (selectedOrganisation) {
              await refreshSelectedOrganisation();
            }

            console.log("Application state refreshed successfully");
          } catch (error) {
            console.error(
              "Failed to refresh application state after payment:",
              error
            );
          }
        }, 5000); // 5 second delay to allow webhook processing

        setIsProcessing(false);
        onPaymentEnd?.();
        onNext();
      } else {
        throw new Error("Payment verification failed");
      }
    } catch (error) {
      console.error("Payment verification error:", error);
      toast.error("Payment verification failed. Please try again.");
      setIsProcessing(false);
      onPaymentEnd?.();
    }
  };

  const handlePayment = async (product: ProductResponse) => {
    setIsProcessing(true);
    onPaymentStart?.();

    try {
      const response = await apiService.initializePlatformTransaction({
        email: user.emailAddress,
        planCode: product.planID,
        organizationId: selectedOrganisation?.id,
      });

      // Redirect to Paystack payment page
      window.location.href = response.authorizationUrl;
    } catch (error) {
      console.error("Failed to initialize Paystack payment:", error);
      toast.error(
        `Failed to initialize payment for ${product.name}. Please try again.`
      );
      setIsProcessing(false);
      onPaymentEnd?.();
    }
  };

  if (isLoadingProducts) {
    return (
      <div className="space-y-4">
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">
            Loading subscription plans...
          </h3>
        </div>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={onPrevious} disabled={isFirstStep}>
            Previous
          </Button>
          <Button disabled className="invisible">
            Next
          </Button>
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="space-y-4">
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">
            No subscription plans available
          </h3>
          <p className="text-sm text-muted-foreground">
            Please contact support or try again later.
          </p>
        </div>
        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={onPrevious} disabled={isFirstStep}>
            Previous
          </Button>
          <Button onClick={onNext}>Skip Payment Step</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <h3 className="text-lg font-semibold">
          Choose a plan that suits your needs
        </h3>
        <p className="text-xs text-muted-foreground">
          For custom plans, contact us
        </p>
      </div>

      <ScrollArea className="h-[400px]">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {products.map((product) => (
            <Card key={product.id} className="w-full flex flex-col">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <span className="text-2xl font-semibold">{product.name}</span>
                  <span className="text-lg font-bold text-primary">
                    {product.price}
                  </span>
                </div>
                <CardDescription className="uppercase text-xs font-thin tracking-wide text-blue-500">
                  {product.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <BrainCircuit size={12} className="text-blue-500" />
                    {product.attributes.gateways} gateways
                  </li>
                  <li className="flex items-center gap-2">
                    <BrainCircuit size={12} className="text-blue-500" />
                    {product.attributes.assets} assets
                  </li>
                  <li className="flex items-center gap-2">
                    <BrainCircuit size={12} className="text-blue-500" />
                    {product.attributes.dataPoints} data points/month
                  </li>
                  {product.attributes.support && (
                    <li className="flex items-center gap-2">
                      <BrainCircuit size={12} className="text-blue-500" />
                      Support included
                    </li>
                  )}
                  {product.attributes.whiteLabel && (
                    <li className="flex items-center gap-2">
                      <BrainCircuit size={12} className="text-blue-500" />
                      White Label
                    </li>
                  )}
                </ul>
              </CardContent>
              <CardFooter className="mt-auto">
                <Button
                  onClick={() => handlePayment(product)}
                  className="w-full"
                  variant="outline"
                  disabled={isProcessing}
                >
                  Choose {product.name} Plan
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="flex flex-col py-6 space-y-2">
          <p className="text-sm">Can't seem to find the right plan?</p>
          <p className="text-sm">
            We have custom solutions that can be tailored to your needs.
          </p>
          <Button
            variant="secondary"
            className="w-full md:w-1/3"
            onClick={() => console.log("Contact us clicked")}
          >
            Contact us
          </Button>
        </div>
      </ScrollArea>

      <div className="flex justify-between pt-4">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isFirstStep || isProcessing}
        >
          Previous
        </Button>
        <Button onClick={onNext} disabled={isProcessing} className="invisible">
          {isLastStep ? "Complete Setup" : "Next"}
        </Button>
      </div>
    </div>
  );
}

export function CreateOrganisationDialog({
  open,
  onOpenChange,
  onComplete,
  initialStep = 1,
}: CreateOrganisationDialogProps) {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [formData, setFormData] = useState<OrganisationFormData>({
    name: "",
  });
  const [isPaymentInProgress, setIsPaymentInProgress] = useState(false);
  const { fetchOrganisations } = useOrganisationStore();

  const updateFormData = (newData: Partial<OrganisationFormData>) => {
    setFormData((prev) => ({ ...prev, ...newData }));
  };

  const handleNext = async () => {
    if (currentStep < TOTAL_STEPS) {
      setCurrentStep((prev) => prev + 1);
    } else {
      // Final step - refresh organization data and complete the dialog
      try {
        await fetchOrganisations();
        console.log("Organization data refreshed on dialog completion");
      } catch (error) {
        console.error(
          "Failed to refresh organization data on completion:",
          error
        );
      }
      onComplete(formData);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const resetForm = () => {
    setCurrentStep(initialStep);
    setFormData({ name: "" });
  };

  // Update current step when initialStep changes
  useEffect(() => {
    console.log("Dialog - initialStep changed to:", initialStep);
    setCurrentStep(initialStep);
  }, [initialStep]);

  const handleOpenChange = (newOpen: boolean) => {
    console.log(
      "Dialog - handleOpenChange:",
      newOpen,
      "currentStep:",
      currentStep
    );
    // Only reset the form when closing the dialog if we're on step 1
    // This prevents losing progress when returning from payment
    if (!newOpen && currentStep === 1) {
      resetForm();
    }
    onOpenChange(newOpen);
  };

  const isStep1Valid = useMemo(() => {
    const result = organisationNameSchema.safeParse({ name: formData.name });
    return result.success;
  }, [formData.name]);

  const getCurrentStepValidation = (): boolean => {
    switch (currentStep) {
      case 1:
        return isStep1Valid;
      case 2:
        return true; // Platform payment step validation handled by payment completion
      case 3:
        return true; // Support payment step validation handled by payment completion
      default:
        return false;
    }
  };

  const renderCurrentStep = () => {
    const stepProps: StepProps = {
      data: formData,
      onDataChange: updateFormData,
      onNext: handleNext,
      onPrevious: handlePrevious,
      isValid: getCurrentStepValidation(),
      isFirstStep: currentStep === 1,
      isLastStep: currentStep === TOTAL_STEPS,
    };

    switch (currentStep) {
      case 1:
        return <Step1OrganisationName {...stepProps} />;
      case 2:
        return (
          <Step2PaymentSelection
            {...stepProps}
            onPaymentStart={() => setIsPaymentInProgress(true)}
            onPaymentEnd={() => setIsPaymentInProgress(false)}
          />
        );
      case 3:
        return (
          <Step3SupportSelection
            {...stepProps}
            onPaymentStart={() => setIsPaymentInProgress(true)}
            onPaymentEnd={() => setIsPaymentInProgress(false)}
          />
        );
      default:
        return null;
    }
  };

  // Calculate progress based on actual completion, not just step number
  const calculateProgress = (): number => {
    let progress = 0;

    // Step 1: Organization name (33%) - only count if valid name is entered
    if (isStep1Valid) {
      progress += 33;
    }

    // Step 2: Platform payment (33%) - count when step 2 is completed
    if (currentStep >= 3) {
      progress += 33;
    }

    // Step 3: Support payment (34%) - count when on final step
    if (currentStep === TOTAL_STEPS) {
      progress += 34;
    }

    return progress;
  };

  const progressPercentage = calculateProgress();

  return (
    <Dialog open={open && !isPaymentInProgress} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Organisation</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>
                Step {currentStep} of {TOTAL_STEPS}
              </span>
              <span>{Math.round(progressPercentage)}% complete</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {renderCurrentStep()}
        </div>
      </DialogContent>
    </Dialog>
  );
}
