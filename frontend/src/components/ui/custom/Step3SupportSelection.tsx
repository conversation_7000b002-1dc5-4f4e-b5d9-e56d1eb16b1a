import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../button";
import { Card, CardContent, CardDescription, CardHeader } from "../card";
import { CreditCard, Loader2 } from "lucide-react";
import { apiService, SupportProductResponse } from "../../../lib/api.service";
import { useUserStore } from "../../../lib/stores/user.store";
import { useOrganisationStore } from "../../../lib/stores/organisation.store";
import { toast } from "sonner";
import { StepProps } from "./create-organisation-dialog";

interface Step3Props extends StepProps {
  onPaymentStart?: () => void;
  onPaymentEnd?: () => void;
}

export function Step3SupportSelection({
  onNext,
  onPrevious,
  isFirstStep,
  isLastStep,
  onPaymentStart,
  onPaymentEnd,
}: Step3Props) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [supportProducts, setSupportProducts] = useState<
    SupportProductResponse[]
  >([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [paymentReference, setPaymentReference] = useState<string | null>(null);
  const user = useUserStore((state) => state);
  const {
    selectedOrganisation,
    refreshSelectedOrganisation,
    fetchOrganisations,
  } = useOrganisationStore();

  // Fetch support products from API
  useEffect(() => {
    const fetchSupportProducts = async () => {
      try {
        setIsLoadingProducts(true);
        const response = await apiService.getSupportProducts();
        setSupportProducts(response.supportProducts);
        console.log("Support products loaded:", response.supportProducts);
      } catch (error) {
        console.error("Failed to fetch support products:", error);
        toast.error("Failed to load support plans. Please try again.");
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchSupportProducts();
  }, []);

  // Handle payment verification when returning from Paystack
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const reference = urlParams.get("reference");

    if (reference && !paymentReference) {
      setPaymentReference(reference);
      verifyPayment(reference);
    }
  }, [paymentReference]);

  const verifyPayment = async (reference: string) => {
    try {
      setIsProcessing(true);
      const result = await apiService.verifySupportTransaction(reference);

      if (result.status === "success") {
        // Find the product that matches the plan code
        const selectedProduct = supportProducts.find(
          (product) => product.planID === result.planCode
        );

        console.log(
          `Support payment successful for ${selectedProduct?.name || "Unknown"} plan.`
        );

        // Show success toast
        toast.success(
          `Support payment successful! ${selectedProduct?.name || "Plan"} activated.`
        );

        setTimeout(async () => {
          try {
            console.log(
              "Refreshing application state after support payment success (with webhook delay)..."
            );

            await fetchOrganisations();

            if (selectedOrganisation) {
              await refreshSelectedOrganisation();
            }

            console.log("Application state refreshed successfully");
          } catch (error) {
            console.error(
              "Failed to refresh application state after payment:",
              error
            );
          }
        }, 5000); // 5 second delay to allow webhook processing

        setIsProcessing(false);
        onPaymentEnd?.();
        onNext();
      } else {
        throw new Error("Support payment verification failed");
      }
    } catch (error) {
      console.error("Support payment verification error:", error);
      toast.error("Support payment verification failed. Please try again.");
      setIsProcessing(false);
      onPaymentEnd?.();
    }
  };

  const handleSupportPayment = async (product: SupportProductResponse) => {
    setIsProcessing(true);
    onPaymentStart?.();

    try {
      console.log("Support payment - product:", product);
      console.log("Support payment - planCode:", product.planID);
      console.log(
        "Support payment - organizationId:",
        selectedOrganisation?.id
      );

      const response = await apiService.initializeSupportTransaction({
        email: user.emailAddress,
        planCode: product.planID,
        organizationId: selectedOrganisation?.id,
      });

      // Redirect to Paystack payment page
      window.location.href = response.authorizationUrl;
    } catch (error) {
      console.error("Failed to initialize Paystack support payment:", error);
      toast.error(
        `Failed to initialize support payment for ${product.name}. Please try again.`
      );
      setIsProcessing(false);
      onPaymentEnd?.();
    }
  };

  if (isLoadingProducts) {
    return (
      <div className="space-y-4">
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">Loading support plans...</h3>
        </div>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={onPrevious} disabled={isFirstStep}>
            Previous
          </Button>
          <Button disabled className="invisible">
            Next
          </Button>
        </div>
      </div>
    );
  }

  if (supportProducts.length === 0) {
    return (
      <div className="space-y-4">
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">No support plans available</h3>
          <p className="text-sm text-muted-foreground">
            Please contact support or try again later.
          </p>
        </div>
        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={onPrevious} disabled={isFirstStep}>
            Previous
          </Button>
          <Button onClick={onNext}>Skip Support Step</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <h3 className="text-lg font-semibold">Choose Your Support Plan</h3>
        <p className="text-sm text-muted-foreground">
          Select a support plan to complete your organisation setup.
        </p>
      </div>

      <div className="grid gap-4">
        {supportProducts.map((product) => (
          <Card
            key={product.id}
            className="relative cursor-pointer transition-all hover:shadow-md"
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold">{product.name}</h4>
                <div className="text-right">
                  <div className="text-lg font-bold">{product.price}</div>
                  <div className="text-xs text-muted-foreground">per month</div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-muted-foreground mb-4">
                {product.description}
              </p>
              <Button
                onClick={() => handleSupportPayment(product)}
                disabled={isProcessing}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Subscribe to {product.name}
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onPrevious} disabled={isFirstStep}>
          Previous
        </Button>
        <Button disabled className="invisible">
          Complete
        </Button>
      </div>
    </div>
  );
}
