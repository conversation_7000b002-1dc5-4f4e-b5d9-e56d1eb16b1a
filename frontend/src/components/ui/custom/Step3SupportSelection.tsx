import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
} from "../card";
import { ScrollArea } from "../scroll-area";
import { BrainCircuit, CreditCard, Loader2 } from "lucide-react";
import { apiService, ProductResponse } from "../../../lib/api.service";
import { useUserStore } from "../../../lib/stores/user.store";
import { useOrganisationStore } from "../../../lib/stores/organisation.store";
import { toast } from "sonner";
import { StepProps } from "./create-organisation-dialog";

interface Step3Props extends StepProps {
  onPaymentStart?: () => void;
  onPaymentEnd?: () => void;
}

export function Step3SupportSelection({
  onNext,
  onPrevious,
  isFirstStep,
  isLastStep,
  onPaymentStart,
  onPaymentEnd,
}: Step3Props) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [supportProducts, setSupportProducts] = useState<ProductResponse[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [paymentReference, setPaymentReference] = useState<string | null>(null);
  const user = useUserStore((state) => state);
  const {
    selectedOrganisation,
    refreshSelectedOrganisation,
    fetchOrganisations,
  } = useOrganisationStore();

  // Fetch support products from API
  useEffect(() => {
    const fetchSupportProducts = async () => {
      try {
        setIsLoadingProducts(true);
        const response = await apiService.getProducts();

        // Check if Products exists and is an array
        if (!response.Products || !Array.isArray(response.Products)) {
          console.error("Invalid support response structure:", response);
          toast.error("Invalid support products data received from server.");
          return;
        }

        // Filter only Support products for Step 3
        const supportProducts = response.Products.filter(
          (product) => product.Type === 1
        );
        setSupportProducts(supportProducts);
        console.log("Support products loaded:", supportProducts);
      } catch (error) {
        console.error("Failed to fetch support products:", error);
        toast.error("Failed to load support plans. Please try again.");
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchSupportProducts();
  }, []);

  // Handle payment verification when returning from Paystack
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const reference = urlParams.get("reference");

    if (reference && !paymentReference) {
      setPaymentReference(reference);
      verifyPayment(reference);
    }
  }, [paymentReference]);

  const verifyPayment = async (reference: string) => {
    try {
      setIsProcessing(true);
      const result = await apiService.verifySupportTransaction(reference);

      if (result.status === "success") {
        // Find the product that matches the plan code
        const selectedProduct = supportProducts.find(
          (product) => product.planID === result.planCode
        );

        console.log(
          `Support payment successful for ${selectedProduct?.name || "Unknown"} plan.`
        );

        // Show success toast
        toast.success(
          `Support payment successful! ${selectedProduct?.name || "Plan"} activated.`
        );

        setTimeout(async () => {
          try {
            console.log(
              "Refreshing application state after support payment success (with webhook delay)..."
            );

            await fetchOrganisations();

            if (selectedOrganisation) {
              await refreshSelectedOrganisation();
            }

            console.log("Application state refreshed successfully");
          } catch (error) {
            console.error(
              "Failed to refresh application state after payment:",
              error
            );
          }
        }, 5000); // 5 second delay to allow webhook processing

        setIsProcessing(false);
        onPaymentEnd?.();
        onNext();
      } else {
        throw new Error("Support payment verification failed");
      }
    } catch (error) {
      console.error("Support payment verification error:", error);
      toast.error("Support payment verification failed. Please try again.");
      setIsProcessing(false);
      onPaymentEnd?.();
    }
  };

  const handleSupportPayment = async (product: ProductResponse) => {
    setIsProcessing(true);
    onPaymentStart?.();

    try {
      console.log("Support payment - product:", product);
      console.log("Support payment - productId:", product.id);
      console.log(
        "Support payment - organizationId:",
        selectedOrganisation?.id
      );

      const response = await apiService.initializeTransaction({
        productId: product.id,
        organizationId: selectedOrganisation?.id || 0,
      });

      // Redirect to Paystack payment page
      window.location.href = response.authorizationUrl;
    } catch (error) {
      console.error("Failed to initialize Paystack support payment:", error);
      toast.error(
        `Failed to initialize support payment for ${product.Name}. Please try again.`
      );
      setIsProcessing(false);
      onPaymentEnd?.();
    }
  };

  if (isLoadingProducts) {
    return (
      <div className="space-y-4">
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">Loading support plans...</h3>
        </div>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={onPrevious} disabled={isFirstStep}>
            Previous
          </Button>
          <Button disabled className="invisible">
            Next
          </Button>
        </div>
      </div>
    );
  }

  if (supportProducts.length === 0) {
    return (
      <div className="space-y-4">
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">No support plans available</h3>
          <p className="text-sm text-muted-foreground">
            Please contact support or try again later.
          </p>
        </div>
        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={onPrevious} disabled={isFirstStep}>
            Previous
          </Button>
          <Button disabled className="invisible">
            Complete
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <h3 className="text-lg font-semibold">
          Choose a support plan that suits your needs
        </h3>
        <p className="text-xs text-muted-foreground">
          For custom support plans, contact us
        </p>
      </div>

      <ScrollArea className="h-[400px]">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {supportProducts.map((product) => (
            <Card key={product.Id} className="w-full flex flex-col">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <span className="text-2xl font-semibold">{product.Name}</span>
                  <span className="text-lg font-bold text-primary">
                    {product.Price}
                  </span>
                </div>
                <CardDescription className="uppercase text-xs font-thin tracking-wide text-blue-500">
                  {product.Description}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                <ul className="space-y-2">
                  {(product.Attributes as any).supportChannels && (
                    <li className="flex items-center gap-2">
                      <BrainCircuit size={12} className="text-blue-500" />
                      Support:{" "}
                      {Array.isArray(
                        (product.Attributes as any).supportChannels
                      )
                        ? (product.Attributes as any).supportChannels.join(", ")
                        : (product.Attributes as any).supportChannels}
                    </li>
                  )}
                  {(product.Attributes as any).responseSLA && (
                    <li className="flex items-center gap-2">
                      <BrainCircuit size={12} className="text-blue-500" />
                      Response SLA: {(product.Attributes as any).responseSLA}
                    </li>
                  )}
                  {(product.Attributes as any).knowledgeBaseAccess && (
                    <li className="flex items-center gap-2">
                      <BrainCircuit size={12} className="text-blue-500" />
                      Knowledge Base Access
                    </li>
                  )}
                  {(product.Attributes as any).technicalConsultation && (
                    <li className="flex items-center gap-2">
                      <BrainCircuit size={12} className="text-blue-500" />
                      Technical Consultation:{" "}
                      {(product.Attributes as any).technicalConsultation}
                    </li>
                  )}
                  {(product.Attributes as any).proactiveMonitoring && (
                    <li className="flex items-center gap-2">
                      <BrainCircuit size={12} className="text-blue-500" />
                      Proactive Monitoring
                    </li>
                  )}
                  {(product.Attributes as any).dedicatedAccountManager && (
                    <li className="flex items-center gap-2">
                      <BrainCircuit size={12} className="text-blue-500" />
                      Dedicated Account Manager
                    </li>
                  )}
                </ul>
              </CardContent>
              <CardFooter className="mt-auto">
                <Button
                  onClick={() => handleSupportPayment(product)}
                  className="w-full"
                  variant="outline"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>Choose {product.Name} Plan</>
                  )}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="flex flex-col py-6 space-y-2">
          <p className="text-sm">Can't seem to find the right support plan?</p>
          <p className="text-sm">
            We have custom support solutions that can be tailored to your needs.
          </p>
          <Button
            variant="secondary"
            className="w-full md:w-1/3"
            onClick={() => console.log("Contact us clicked")}
          >
            Contact us
          </Button>
        </div>
      </ScrollArea>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onPrevious} disabled={isFirstStep}>
          Previous
        </Button>
        <Button disabled className="invisible">
          Complete
        </Button>
      </div>
    </div>
  );
}
