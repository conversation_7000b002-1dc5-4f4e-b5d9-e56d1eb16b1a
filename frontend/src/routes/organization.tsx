import { createFileRoute } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import OrganizationComponent from "../components/routes/organizations/OrganizationComponent";
import { CreateOrganisationDialog } from "../components/ui/custom/create-organisation-dialog";

export const Route = createFileRoute("/organization")({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      component: (search.component as string) || "dashboard",
      showPaymentDialog: (search.showPaymentDialog as string) === "true",
      paymentStep: parseInt(search.paymentStep as string) || 1,
    };
  },
  component: () => {
    const { component, showPaymentDialog, paymentStep } = Route.useSearch();
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    // Handle payment dialog opening based on URL parameters
    useEffect(() => {
      if (showPaymentDialog) {
        setIsDialogOpen(true);
      }
    }, [showPaymentDialog]);

    return (
      <div className="w-full h-full">
        <OrganizationComponent activeComponent={component} />

        {/* Payment dialog for continuing the payment flow */}
        <CreateOrganisationDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          onComplete={() => setIsDialogOpen(false)}
          initialStep={paymentStep}
        />
      </div>
    );
  },
});
