import { createFileRoute } from "@tanstack/react-router";
import OrganizationComponent from "../components/routes/organizations/OrganizationComponent";

export const Route = createFileRoute("/organization")({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      component: (search.component as string) || "dashboard",
    };
  },
  component: () => {
    const { component } = Route.useSearch();

    return (
      <div className="w-full h-full">
        <OrganizationComponent activeComponent={component} />
      </div>
    );
  },
});
