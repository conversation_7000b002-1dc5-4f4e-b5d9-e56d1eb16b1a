import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import OrganizationComponent from "../components/routes/organizations/OrganizationComponent";
import { CreateOrganisationDialog } from "../components/ui/custom/create-organisation-dialog";

export const Route = createFileRoute("/organization")({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      component: (search.component as string) || "dashboard",
      showPaymentDialog: (search.showPaymentDialog as string) === "true",
      paymentStep: parseInt(search.paymentStep as string) || 1,
    };
  },
  component: () => {
    const { component, showPaymentDialog, paymentStep } = Route.useSearch();
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const navigate = useNavigate();

    // Handle payment dialog opening based on URL parameters
    useEffect(() => {
      console.log(
        "Organization route - showPaymentDialog:",
        showPaymentDialog,
        "paymentStep:",
        paymentStep
      );
      if (showPaymentDialog) {
        setIsDialogOpen(true);
      }
    }, [showPaymentDialog, paymentStep]);

    // Handle dialog close - clear URL parameters
    const handleDialogOpenChange = (open: boolean) => {
      setIsDialogOpen(open);

      // If dialog is closed and URL has showPaymentDialog parameter, clear it
      if (!open && showPaymentDialog) {
        navigate({
          to: "/organization",
          search: { component },
        });
      }
    };

    return (
      <div className="w-full h-full">
        <OrganizationComponent activeComponent={component} />

        {/* Payment dialog for continuing the payment flow */}
        <CreateOrganisationDialog
          open={isDialogOpen}
          onOpenChange={handleDialogOpenChange}
          onComplete={() => handleDialogOpenChange(false)}
          initialStep={paymentStep}
        />
      </div>
    );
  },
});
