import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { apiService } from "../lib/api.service";
import { useOrganisationStore } from "../lib/stores/organisation.store";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

export const Route = createFileRoute("/payment-success")({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      reference: (search.reference as string) || "",
      type: (search.type as string) || "platform",
    };
  },
  component: PaymentSuccessComponent,
});

function PaymentSuccessComponent() {
  const { reference, type } = Route.useSearch();
  const navigate = useNavigate();
  const [isVerifying, setIsVerifying] = useState(true);
  const { fetchOrganisations, refreshSelectedOrganisation } = useOrganisationStore();

  useEffect(() => {
    const verifyPayment = async () => {
      if (!reference) {
        toast.error("No payment reference found");
        navigate({ to: "/organization", search: { component: "dashboard" } });
        return;
      }

      try {
        setIsVerifying(true);
        
        // Verify the payment based on the type
        const result = type === "support" 
          ? await apiService.verifySupportTransaction(reference)
          : await apiService.verifyPlatformTransaction(reference);

        if (result.status === "success") {
          toast.success(`Payment successful! Your ${type} subscription is now active.`);
          
          // Refresh organization data
          await fetchOrganisations();
          await refreshSelectedOrganisation();
          
          // If this was a platform payment, redirect to support payment
          if (type === "platform") {
            // Redirect to organization page with dialog open for support payment
            navigate({ 
              to: "/organization", 
              search: { 
                component: "dashboard",
                showPaymentDialog: "true",
                paymentStep: "3" // Support payment step
              } 
            });
          } else {
            // If this was a support payment, just go to organization dashboard
            navigate({ to: "/organization", search: { component: "dashboard" } });
          }
        } else {
          toast.error("Payment verification failed. Please contact support.");
          navigate({ to: "/organization", search: { component: "dashboard" } });
        }
      } catch (error) {
        console.error("Payment verification error:", error);
        toast.error("Failed to verify payment. Please contact support.");
        navigate({ to: "/organization", search: { component: "dashboard" } });
      } finally {
        setIsVerifying(false);
      }
    };

    verifyPayment();
  }, [reference, type, navigate, fetchOrganisations, refreshSelectedOrganisation]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        <h1 className="text-2xl font-bold mb-4">Processing Your Payment</h1>
        
        {isVerifying ? (
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="text-muted-foreground">
              Please wait while we verify your payment...
            </p>
          </div>
        ) : (
          <p>Redirecting you to your organization dashboard...</p>
        )}
      </div>
    </div>
  );
}
