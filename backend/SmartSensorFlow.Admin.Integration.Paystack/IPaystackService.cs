using SmartSensorFlow.Admin.Integration.Paystack.Enums;
using SmartSensorFlow.Admin.Integration.Paystack.Models;

namespace SmartSensorFlow.Admin.Integration.Paystack;

public interface IPaystackService
{
    Task<InitializeTransactionResponse> InitializeTransaction(string email, string planCode, PaystackSubscriptionType subscriptionType, int amount, object? metadata = null);
    Task<VerifyTransactionResponse> VerifyTransaction(string reference, PaystackSubscriptionType subscriptionType);
    Task<bool> CancelSubscription(string subscriptionCode, string emailToken, PaystackSubscriptionType subscriptionType);
}
