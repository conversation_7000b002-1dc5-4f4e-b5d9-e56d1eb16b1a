using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using SmartSensorFlow.Admin.Integration.Paystack.Config;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;
using SmartSensorFlow.Admin.Integration.Paystack.Models;

namespace SmartSensorFlow.Admin.Integration.Paystack;

public class PaystackService : IPaystackService
{
    private readonly HttpClient _httpClient;
    private readonly PaystackPlatformConfiguration _platformConfiguration;
    private readonly PaystackSupportConfiguration _supportConfiguration;
    private readonly ILogger<PaystackService> _logger;
    private static readonly JsonSerializerOptions _jsonSerializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = true
    };

    public PaystackService(
        PaystackPlatformConfiguration platformConfiguration,
        PaystackSupportConfiguration supportConfiguration,
        ILogger<PaystackService> logger)
    {
        _platformConfiguration = platformConfiguration;
        _supportConfiguration = supportConfiguration;
        _httpClient = new HttpClient();
        _httpClient.DefaultRequestHeaders.Accept.Clear();
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        _httpClient.BaseAddress = new Uri("https://api.paystack.co/");
        _logger = logger;
    }

    public async Task<InitializeTransactionResponse> InitializeTransaction(string email, string planCode, PaystackSubscriptionType subscriptionType, object? metadata = null)
    {
        try
        {
            // Set the appropriate secret key based on subscription type
            string secretKey = subscriptionType == PaystackSubscriptionType.Platform
                ? _platformConfiguration.SecretKey
                : _supportConfiguration.SecretKey;

            string callbackUrlBase = subscriptionType == PaystackSubscriptionType.Platform
                ? _platformConfiguration.CallbackUrlBase
                : _supportConfiguration.CallbackUrlBase;

            // Set authorization header for this request
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", secretKey);

            var request = new InitializeTransactionRequest
            {
                Email = email,
                Plan = planCode,
                Metadata = metadata
            };

            if (!string.IsNullOrEmpty(callbackUrlBase))
            {
                request.CallbackUrl = callbackUrlBase;
            }

            var jsonRequest = JsonSerializer.Serialize(request, _jsonSerializerOptions);
            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("transaction/initialize", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to initialize Paystack transaction: {Response}", responseContent);
                return new InitializeTransactionResponse
                {
                    Status = false,
                    Message = $"Failed to initialize transaction: {response.StatusCode}"
                };
            }

            var result = JsonSerializer.Deserialize<InitializeTransactionResponse>(responseContent, _jsonSerializerOptions);
            return result ?? new InitializeTransactionResponse { Status = false, Message = "Failed to deserialize response" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing Paystack transaction");
            return new InitializeTransactionResponse
            {
                Status = false,
                Message = $"Exception: {ex.Message}"
            };
        }
    }

    public async Task<VerifyTransactionResponse> VerifyTransaction(string reference, PaystackSubscriptionType subscriptionType)
    {
        try
        {
            // Set the appropriate secret key based on subscription type
            string secretKey = subscriptionType == PaystackSubscriptionType.Platform
                ? _platformConfiguration.SecretKey
                : _supportConfiguration.SecretKey;

            // Set authorization header for this request
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", secretKey);

            var response = await _httpClient.GetAsync($"transaction/verify/{reference}");
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to verify Paystack transaction: {Response}", responseContent);
                return new VerifyTransactionResponse
                {
                    Status = false,
                    Message = $"Failed to verify transaction: {response.StatusCode}"
                };
            }

            var result = JsonSerializer.Deserialize<VerifyTransactionResponse>(responseContent, _jsonSerializerOptions);
            return result ?? new VerifyTransactionResponse { Status = false, Message = "Failed to deserialize response" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying Paystack transaction");
            return new VerifyTransactionResponse
            {
                Status = false,
                Message = $"Exception: {ex.Message}"
            };
        }
    }

    public async Task<bool> CancelSubscription(string subscriptionCode, string emailToken, PaystackSubscriptionType subscriptionType)
    {
        try
        {
            // Set the appropriate secret key based on subscription type
            string secretKey = subscriptionType == PaystackSubscriptionType.Platform
                ? _platformConfiguration.SecretKey
                : _supportConfiguration.SecretKey;

            // Set authorization header for this request
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", secretKey);

            var request = new CancelSubscriptionRequest
            {
                Code = subscriptionCode,
                Token = emailToken
            };

            var jsonRequest = JsonSerializer.Serialize(request, _jsonSerializerOptions);
            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("subscription/disable", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to cancel Paystack subscription: {Response}", responseContent);
                return false;
            }

            _logger.LogInformation("Successfully cancelled Paystack subscription: {SubscriptionCode}", subscriptionCode);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling Paystack subscription");
            return false;
        }
    }
}
