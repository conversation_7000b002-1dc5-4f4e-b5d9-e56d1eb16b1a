using System.Text.Json;
using Dapper;
using Npgsql;
using SmartSensorFlow.Admin.Common.Configuration;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.Data.DataServices.Dto;
using SmartSensorFlow.Admin.Data.DataServices.Models;

namespace SmartSensorFlow.Admin.Data.DataServices;

public class QueryHandler(SQLConfiguration config) : IQueryHandler
{
    public async Task<int> CreateOrganisation(int userId, string name)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"INSERT INTO ""Organisation"" (
            ""Name"",
            ""OwnerId"",
            ""CreatedBy"",
            ""ModifiedBy"") VALUES
            (
                @name,
                @userId,
                @userId,
                @userId
            ) RETURNING ""Id""";
        var insertedId = await connection.ExecuteScalarAsync<int>(sql, new { name, userId });
        return insertedId;
    }

    public async Task<OrganisationDataModel?> GetOrganisationById(int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Organisation\" WHERE \"Id\" = @organisationId";
        var orgs = await connection.QueryAsync<OrganisationDataModel>(sql, new { organisationId });
        return orgs.FirstOrDefault();
    }

    public async Task<List<OrganisationDataModel>> GetByOwner(int userId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT *, 1 AS UserStatus FROM \"Organisation\" WHERE \"OwnerId\" = @userId";
        var orgs = await connection.QueryAsync<OrganisationDataModel>(sql, new { userId });
        return [.. orgs];
    }

    public async Task<List<OrganisationDataModel>> GetUserOrganisations(int userId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT
            o.""Id"",
            o.""Name"",
            o.""OwnerId"",
            o.""Status"",
            o.""CreatedBy"",
            o.""CreatedAt"",
            o.""ModifiedBy"",
            o.""ModifiedAt"",
            uo.""Status"" AS UserStatus
        FROM 
            ""Organisation"" o
            INNER JOIN ""UserToOrganisationBridge"" uo ON uo.""OrganisationId"" = o.""Id""
            WHERE uo.""UserId"" = @userId";
        var orgs = await connection.QueryAsync<OrganisationDataModel>(sql, new { userId });
        return [.. orgs];
    }

    public async Task<List<ProductDataModel>> GetAllProducts()
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Products\"";
        var products = await connection.QueryAsync<ProductDataModel>(sql);
        return [.. products];
    }

    public async Task AddSubscription(
        int productId,
        int organisationId,
        string subscriptionId,
        BillingCycle billingCycle,
        int price,
        string currency,
        DateTimeOffset nextPaymentDate,
        DateTimeOffset initiationDate)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"INSERT INTO ""Subscriptions""
        (
            ""ProductId"",
            ""OrganisationId"",
            ""PaddleSubscriptionId"",
            ""BillingCycle"",
            ""Price"",
            ""Currency"",
            ""NextPaymentDate"",
            ""InitiationDate""
        ) Values (
            @productId,
            @organisationId,
            @subscriptionId,
            @billingCycle,
            @price,
            @currency,
            @nextPaymentDate,
            @initiationDate
        )";
        await connection.ExecuteAsync(sql, new
        {
            productId,
            organisationId,
            subscriptionId,
            billingCycle,
            price,
            currency,
            nextPaymentDate,
            initiationDate
        });
    }

    public async Task<SubscriptionDataModel?> GetSubscriptionByPaddleSubscriptionId(string subscriptionId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Subscriptions\" WHERE \"PaddleSubscriptionId\" = @subscriptionId";
        var subscriptions = await connection.QueryAsync<SubscriptionDataModel>(sql, new { subscriptionId });
        return subscriptions.FirstOrDefault();
    }

    public async Task<List<SubscriptionDto>> GetSubscriptionByOrganisationId(int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT
            s.""Id"",
            p.""Name"",
            s.""Status"",
            s.""BillingCycle"",
            s.""Price"",
            s.""Currency"",
            s.""NextPaymentDate"",
            s.""InitiationDate"",
            s.""CancellationDate""
        FROM ""Subscriptions"" s
        INNER JOIN ""Products"" p on p.""Id"" = s.""ProductId""
        WHERE ""OrganisationId"" = @organisationId";
        var subscriptions = await connection.QueryAsync<SubscriptionDto>(sql, new { organisationId });
        return [.. subscriptions];
    }

    public async Task<SubscriptionDataModel?> GetSubscriptionBySubscriptionId(int subscriptionId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Subscriptions\" WHERE \"Id\" = @subscriptionId";
        var subscriptions = await connection.QueryAsync<SubscriptionDataModel>(sql, new { subscriptionId });
        return subscriptions.FirstOrDefault();
    }

    public async Task<List<ModulesDataModel>> GetModules()
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Modules\"";
        var subscriptions = await connection.QueryAsync<ModulesDataModel>(sql);
        return [.. subscriptions];
    }

    public async Task<bool> IsOrganisationSubscribedToModule(int organisationId, int moduleId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"ModuleToOrganisationBridge\" WHERE \"OrganisationId\" = @organisationId AND \"ModuleId\" = @moduleId";
        var links = await connection.QueryAsync<ModuleToOrganisationBridgeDataModel>(sql, new { organisationId, moduleId });
        return links.Any();
    }

    public async Task LinkModuleToOrganisation(int moduleId, int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "INSERT INTO \"ModuleToOrganisationBridge\" (\"ModuleId\", \"OrganisationId\") VALUES ( @moduleId, @organisationId)";
        await connection.ExecuteAsync(sql, new { organisationId, moduleId });
    }

    public async Task<List<ModulesDataModel>> GetModulesByOrganisationID(int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT m.* 
            FROM ""Modules"" m 
            INNER JOIN ""ModuleToOrganisationBridge"" ob ON ob.""ModuleId"" = m.""Id"" 
            WHERE ob.""OrganisationId"" = @organisationId";
        return [.. await connection.QueryAsync<ModulesDataModel>(sql, new { organisationId })];
    }

    public async Task<bool> IsUserInOrganisation(int userId, int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT *  
            FROM ""UserToOrganisationBridge""
            WHERE ""OrganisationId"" = @organisationId
            AND ""UserId"" = @userId";
        var result = await connection.QueryAsync<UserToOrganisationBridgeDataModel>(sql, new { organisationId, userId });
        return result.Any();
    }

    public async Task<List<InviteDateModel>> GetInviteByEmailAddress(string emailAddress)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Invites\" WHERE \"Email\" = @emailAddress";
        return [.. await connection.QueryAsync<InviteDateModel>(sql, new { emailAddress })];
    }

    public async Task<InviteDateModel?> GetInviteByInviteKey(string key)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Invites\" WHERE \"InviteKey\" = @key";
        var result = await connection.QueryAsync<InviteDateModel>(sql, new { key });
        return result.FirstOrDefault();
    }

    public async Task CreateOrganisationInvite(
        string inviteKey,
        string emailAddress,
        int organisationId,
        int createdBy,
        string? firstName,
        string? lastName)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var inviteExpireDate = DateTime.Now.AddDays(1).ToUniversalTime();
        var inviteType = InviteType.Organisation;
        var inviteMetaData = JsonSerializer.Serialize(new OrganisationInviteMetaDataModel()
        {
            FirstName = firstName,
            LastName = lastName
        });
        var sql = @"INSERT INTO ""Invites"" (
            ""Email"",
            ""InviteKey"",
            ""InviteType"",
            ""OrganisationId"",
            ""InviteExpireDate"",
            ""InviteMetadata"",
            ""CreatedBy"")
        VALUES (@emailAddress, @inviteKey, @inviteType, @organisationId, @inviteExpireDate, @inviteMetaData::json, @createdBy)";
        await connection.ExecuteAsync(sql, new { emailAddress, inviteKey, inviteType, organisationId, inviteExpireDate, @inviteMetaData, createdBy });
    }

    public async Task<List<UserDto>> GetOrganisationUsers(int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT
            u.""Id"",
            ""FirstName"",
            ""LastName"",
            ""EmailAddress"",
            u.""Status"" AS UserStatus,
            o.""Status"" AS OrganisationStatus,
            false AS IsOwner
        FROM ""Users"" u
        INNER JOIN ""UserToOrganisationBridge"" o ON o.""UserId"" = u.""Id""
        WHERE o.""OrganisationId"" = @organisationId
        AND o.""Status"" != 3"; // Only get active or suspended users
        var result = await connection.QueryAsync<UserDto>(sql, new { organisationId });
        return [.. result];
    }

    public async Task<UserDto> GetOrganisationOwner(int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT
            u.""Id"",
            u.""FirstName"",
            u.""LastName"",
            u.""EmailAddress"",
            u.""Status"" AS UserStatus,
            o.""Status"" AS OrganisationStatus,
            true AS IsOwner
        FROM ""Users"" u
        INNER JOIN ""Organisation"" o ON o.""OwnerId"" = u.""Id""
        WHERE o.""Id"" = @organisationId";
        var result = await connection.QueryAsync<UserDto>(sql, new { organisationId });
        return result.First();
    }

    public async Task RemoveUserFromOrganisation(int userId, int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"UPDATE ""UserToOrganisationBridge""
                SET ""Status"" = 3
                WHERE ""OrganisationId"" = @organisationId
                AND ""UserId"" = @userId";
        await connection.ExecuteAsync(sql, new { organisationId, userId });
    }

    public async Task SuspendUserFromOrganisation(int userId, int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"UPDATE ""UserToOrganisationBridge""
                SET ""Status"" = 2
                WHERE ""OrganisationId"" = @organisationId
                AND ""UserId"" = @userId";
        await connection.ExecuteAsync(sql, new { organisationId, userId });
    }

    public async Task ActivateUserOnOrganisation(int userId, int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"UPDATE ""UserToOrganisationBridge""
                SET ""Status"" = 1
                WHERE ""OrganisationId"" = @organisationId
                AND ""UserId"" = @userId";
        await connection.ExecuteAsync(sql, new { organisationId, userId });
    }

    public async Task<InviteBlocksDataModel?> UserHasBlockedInvitesById(int userId, int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT * FROM ""InviteBlocks""
            WHERE ""UserId"" = @userId
            AND ""OrganisationId"" = @organisationId";
        var result = await connection.QueryAsync<InviteBlocksDataModel>(sql, new { userId, organisationId });
        return result.FirstOrDefault();
    }

    public async Task<EmailInviteBlocksDataModel?> UserHasBlockedInvitesByEmail(string email, int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT * FROM ""EmailInviteBlocks""
            WHERE ""Email"" = @email
            AND ""OrganisationId"" = @organisationId";
        var result = await connection.QueryAsync<EmailInviteBlocksDataModel>(sql, new { email, organisationId });
        return result.FirstOrDefault();
    }

    public async Task AcceptInvite(string inviteKey)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"UPDATE ""Invites"" SET ""InviteStatus"" = 1 WHERE ""InviteKey"" = @inviteKey";
        await connection.ExecuteAsync(sql, new { inviteKey });
    }

    public async Task AddUserToOrganisation(int organisationId, int userId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"INSERT INTO ""UserToOrganisationBridge"" (
            ""OrganisationId"",
            ""UserId"")
        VALUES  (@organisationId, @userId)";
        await connection.ExecuteAsync(sql, new { organisationId, userId });
    }

    public async Task RejectInvite(string inviteKey, string? rejectReason)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"UPDATE ""Invites"" SET ""InviteStatus"" = 2, ""RejectionReason"" = @rejectReason WHERE ""InviteKey"" = @inviteKey";
        await connection.ExecuteAsync(sql, new { rejectReason, inviteKey });
    }

    public async Task BlockAsExistingUser(int userId, int organisationId, string? blockReason)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"INSERT INTO ""InviteBlocks"" (
            ""UserId"",
            ""OrganisationId"",
            ""Reason"")
        VALUES  (@userId, @organisationId, @blockReason)";
        await connection.ExecuteAsync(sql, new { userId, organisationId, blockReason });
    }

    public async Task BlockAsNonExistingUser(string email, int organisationId, string? blockReason)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"INSERT INTO ""EmailInviteBlocks"" (
            ""Email"",
            ""OrganisationId"",
            ""Reason"")
        VALUES  (@email, @organisationId, @blockReason)";
        await connection.ExecuteAsync(sql, new { email, organisationId, blockReason });
    }

    public async Task CreateRole(string name, string description, int organisationId, int userId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"INSERT INTO ""Roles"" (
            ""RoleName"",
            ""RoleDescription"",
            ""OrganisationId"",
            ""CreatedBy"")
        VALUES  (@name, @description, @organisationId, @userId)";
        await connection.ExecuteAsync(sql, new { name, description, organisationId, userId });
    }

    public async Task<List<RoleDataModel>> GetOrganisationRoles(int organisationId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT
            r.""Id"",
            r.""RoleName"",
            r.""RoleDescription"",
            r.""OrganisationId"",
            u.""FirstName"" AS CreatedBy,
            r.""CreatedAt""
        FROM ""Roles"" r
        INNER JOIN ""Users"" u on u.""Id"" = r.""CreatedBy""
        WHERE r.""OrganisationId"" = @organisationId";
        var result = await connection.QueryAsync<RoleDataModel>(sql, new { organisationId });
        return [.. result];
    }

    public async Task<List<PermissionData>> GetPermissionsOfRole(int roleId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT
            p.""Id"",
            p.""PermissionName"",
            p.""PermissionDescription"",
            p.""Scope"",
            p.""Domain""
            FROM ""Permissions"" p
            INNER JOIN  ""PermissionToRoleBridge"" pb ON pb.""PermissionId"" = p.""Id""
            WHERE pb.""RoleId"" = @roleId";
        var result = await connection.QueryAsync<PermissionData>(sql, new { roleId });
        return [.. result];
    }

    public async Task AssignPermissionToRole(int roleId, int permissionId, int userId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"INSERT INTO ""PermissionToRoleBridge""
            (
                ""RoleId"",
                ""PermissionId"",
                ""AssignedBy""
            )
            VALUES (
                @roleId,
                @permissionId,
                @userId
            )";
        await connection.ExecuteAsync(sql, new { roleId, permissionId, userId });
    }

    public async Task<List<RoleDataModel>> GetRolesOfUser(int userId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"SELECT
            r.""Id"",
            r.""RoleName"",
            r.""RoleDescription"",
            r.""OrganisationId"",
            r.""CreatedBy"",
            r.""CreatedAt""
            FROM ""Roles"" r
            INNER JOIN  ""UserToRoleBridge"" ub ON ub.""RoleId"" = r.""Id""
            WHERE ub.""UserId"" = @userId";
        var result = await connection.QueryAsync<RoleDataModel>(sql, new { userId });
        return [.. result];
    }

    public async Task AssignRoleToUser(int userId, int roleId, int assignedBy)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = @"INSERT INTO ""UserToRoleBridge""
            (
                ""UserId"",
                ""RoleId"",
                ""AssignedBy""
            )
            VALUES (
                @userId,
                @roleId,
                @assignedBy
            )";
        await connection.ExecuteAsync(sql, new { userId, roleId, assignedBy });
    }
}