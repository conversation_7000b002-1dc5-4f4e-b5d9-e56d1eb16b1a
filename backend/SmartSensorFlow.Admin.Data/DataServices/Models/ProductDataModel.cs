namespace SmartSensorFlow.Admin.Data.DataServices.Models;

public class ProductDataModel
{
    public required int Id { get; set; }

    public required string PlanID { get; set; }

    public required string Name { get; set; }

    public required string Price { get; set; }

    public required string Description { get; set; }

    public required string Attributes { get; set; }

    public required string CreatedBy { get; set; }

    public required DateTimeOffset CreatedAt { get; set; }

    public required string ModifiedBy { get; set; }

    public required DateTimeOffset ModifiedAt { get; set; }
}
