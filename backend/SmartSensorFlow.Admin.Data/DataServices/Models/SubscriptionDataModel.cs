using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Data.DataServices.Models;

public class SubscriptionDataModel
{
    public required int Id { get; set; }
    
    public required int ProductId { get; set; }
    
    public required int OrganisationId { get; set; }

    public required string PaddleSubscriptionId { get; set; }
    
    public required SubscriptionStatus Status { get; set; }
    
    public required BillingCycle BillingCycle { get; set; }
    
    public required int Price { get; set; }

    public required string Currency { get; set; }
    
    public required DateTimeOffset NextPaymentDate { get; set; }
    
    public required DateTimeOffset InitiationDate { get; set; }
    
    public DateTimeOffset? CancellationDate { get; set; }
    
    // public required DateTimeOffset CreatedAt { get; set; }
    
    // public required DateTimeOffset ModifiedAt { get; set; }
}
