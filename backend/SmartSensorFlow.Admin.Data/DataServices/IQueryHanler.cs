using System.Runtime.CompilerServices;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.Data.DataServices.Dto;
using SmartSensorFlow.Admin.Data.DataServices.Models;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;

namespace SmartSensorFlow.Admin.Data.DataServices;

public interface IQueryHandler
{
    Task<int> CreateOrganisation(int userId, string name);

    Task<OrganisationDataModel?> GetOrganisationById(int organisationId);

    Task<List<OrganisationDataModel>> GetByOwner(int userId);

    Task<List<OrganisationDataModel>> GetUserOrganisations(int userId);

    Task<List<ProductDataModel>> GetAllProducts();

    Task<List<SupportProductDataModel>> GetAllSupportProducts();

    Task<ProductDataModel?> GetProductByPlanId(string planId);

    Task<SupportProductDataModel?> GetSupportProductByPlanId(string planId);

    Task<UserDataModel?> GetUserByEmail(string email);

    Task<List<OrganisationDataModel>> GetOrganisationsByUserId(int userId);

    // Legacy Paddle subscription methods
    Task AddSubscription(
        int productId,
        int organisationId,
        string subscriptionId,
        BillingCycle billingCycle,
        int price,
        string currency,
        DateTimeOffset nextPaymentDate,
        DateTimeOffset initiationDate);

    Task<SubscriptionDataModel?> GetSubscriptionByPaddleSubscriptionId(string subscriptionId);

    // Paystack subscription methods
    Task AddOrUpdatePaystackPlatformSubscription(
        int productId,
        int organisationId,
        string subscriptionId,
        long price,
        string currency,
        DateTimeOffset nextPaymentDate);

    Task AddOrUpdatePaystackSupportSubscription(
        int supportProductId,
        int organisationId,
        string subscriptionId,
        long price,
        string currency,
        DateTimeOffset nextPaymentDate);

    Task<SubscriptionDataModel?> GetSubscriptionByPaystackPlatformSubscriptionId(string subscriptionId);

    Task<SubscriptionDataModel?> GetSubscriptionByPaystackSupportSubscriptionId(string subscriptionId);

    Task CancelPaystackSubscription(string subscriptionId, PaystackSubscriptionType subscriptionType);

    Task<List<SubscriptionDto>> GetSubscriptionByOrganisationId(int organisationId);

    Task<SubscriptionDataModel?> GetSubscriptionBySubscriptionId(int subscriptionId);

    Task<List<ModulesDataModel>> GetModules();

    Task<bool> IsOrganisationSubscribedToModule(int organisationId, int moduleId);

    Task LinkModuleToOrganisation(int moduleId, int organisationId);

    Task<List<ModulesDataModel>> GetModulesByOrganisationID(int organisationId);

    Task<bool> IsUserInOrganisation(int userId, int organisationId);

    Task<List<InviteDateModel>> GetInviteByEmailAddress(string emailAddress);

    Task<InviteDateModel?> GetInviteByInviteKey(string key);

    Task CreateOrganisationInvite(
        string inviteKey,
        string emailAddress,
        int organisationId,
        int createdBy,
        string? firstName,
        string? lastName);

    Task<List<UserDto>> GetOrganisationUsers(int organisationId);

    Task<UserDto> GetOrganisationOwner(int organisationId);

    Task RemoveUserFromOrganisation(int userId, int organisationId);

    Task SuspendUserFromOrganisation(int userId, int organisationId);

    Task ActivateUserOnOrganisation(int userId, int organisationId);

    Task<InviteBlocksDataModel?> UserHasBlockedInvitesById(int userId, int organisationId);

    Task<EmailInviteBlocksDataModel?> UserHasBlockedInvitesByEmail(string email, int organisationId);

    Task AcceptInvite(string inviteKey);

    Task RejectInvite(string inviteKey, string? rejectReason);

    Task BlockAsExistingUser(int userId, int organisationId, string? blockReason);

    Task BlockAsNonExistingUser(string email, int organisationId, string? blockReason);

    Task AddUserToOrganisation(int organisationId, int userId);

    Task CreateRole(string name, string description, int organisationId, int userId);

    Task<List<RoleDataModel>> GetOrganisationRoles(int organisationId);

    Task<List<PermissionData>> GetPermissionsOfRole(int roleId);

    Task<List<RoleDataModel>> GetRolesOfUser(int userId);

    Task AssignPermissionToRole(int roleId, int permissionId, int userId);

    Task AssignRoleToUser(int roleId, int userId, int assignedBy);
}