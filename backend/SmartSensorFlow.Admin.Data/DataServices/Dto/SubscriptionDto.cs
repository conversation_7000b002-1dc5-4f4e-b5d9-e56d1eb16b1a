using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Data.DataServices.Dto;

public class SubscriptionDto
{
    public required int Id { get; set; }
    
    public required string ProductName { get; set; }
    
    public required SubscriptionStatus Status { get; set; }
    
    public required BillingCycle BillingCycle { get; set; }
    
    public required int Price { get; set; }

    public required string Currency { get; set; }
    
    public required DateTimeOffset NextPaymentDate { get; set; }
    
    public required DateTimeOffset InitiationDate { get; set; }
    
    public DateTimeOffset? CancellationDate { get; set; }
}
