using System.Text.Json;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Products.Models;
using SmartSensorFlow.Admin.Api.Controllers.Billing.SupportProducts.Models;
using SmartSensorFlow.Admin.Data.DataServices.Models;

namespace SmartSensorFlow.Admin.Api.Extensions;

public static class ProductExtensions
{
    public static GetProductsResponse ToResponse(this List<ProductDataModel> products)
    {
        return new GetProductsResponse
        {
            Products = products.Select(p => new ProductResponse
            {
                Id = p.Id,
                PlanID = p.PlanID,
                Name = p.Name,
                Price = p.Price,
                Description = p.Description,
                Attributes = JsonSerializer.Deserialize<Dictionary<string, object>>(p.Attributes) ?? new Dictionary<string, object>()
            }).ToList()
        };
    }

    public static GetSupportProductsResponse ToResponse(this List<SupportProductDataModel> products)
    {
        return new GetSupportProductsResponse
        {
            SupportProducts = products.Select(p => new SupportProductResponse
            {
                Id = p.Id,
                PlanID = p.PlanID,
                Name = p.Name,
                Price = p.Price,
                Description = p.Description,
                Attributes = JsonSerializer.Deserialize<Dictionary<string, object>>(p.Attributes) ?? new Dictionary<string, object>()
            }).ToList()
        };
    }
}
