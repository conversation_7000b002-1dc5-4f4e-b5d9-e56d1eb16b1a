using System.Text.Json;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Products.Models;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Subscription.GetSubscription.Models;
using SmartSensorFlow.Admin.Api.Controllers.Modules.GetModules.Models;
using SmartSensorFlow.Admin.Api.Controllers.Permissions.Response;
using SmartSensorFlow.Admin.Api.Controllers.Roles.GetRoles.Models;
using SmartSensorFlow.Admin.Api.Controllers.Users.GetOrganisationUsers.Models;
using SmartSensorFlow.Admin.Data.DataServices.Dto;
using SmartSensorFlow.Admin.Data.DataServices.Models;

namespace SmartSensorFlow.Admin.Api.Extensions;

public static class ResponseExtensions
{
    public static GetProductsResponse ToResponse(this List<ProductDataModel> data)
    {
        var response = new GetProductsResponse()
        {
            Products = [.. data.Select(ToResponse)]
        };
        return response;
    }

    public static ProductResponse ToResponse(this ProductDataModel data)
    {
        // TODO: Fix this
        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        var response = new ProductResponse()
        {
            Id = data.Id,
            PlanID = data.PlanID,
            Attributes = JsonSerializer.Deserialize<ProductAttributes>(data.Attributes, options)!,
            Description = data.Description,
            Name = data.Name,
            Price = data.Price
        };
        return response;
    }

    public static GetSubscriptionResponse ToResponse(this List<SubscriptionDto> data)
    {
        var response = new GetSubscriptionResponse()
        {
            Subscriptions = [.. data.Select(ToResponse)]
        };
        return response;
    }

    private static SubscriptionResponse ToResponse(this SubscriptionDto data)
    {
        var response = new SubscriptionResponse()
        {
            Id = data.Id,
            ProductName = data.ProductName,
            Status = data.Status,
            BillingCycle = data.BillingCycle,
            Price = data.Price / 100,
            Currency = data.Currency,
            NextPaymentDate = data.NextPaymentDate,
            InitiationDate = data.InitiationDate,
            CancellationDate = data.CancellationDate
        };
        return response;
    }

    public static GetModuleResponse ToResponse(this List<ModulesDataModel> data)
    {
        var response = new GetModuleResponse()
        {
            Modules = [.. data.Select(ToResponse)]
        };
        return response;
    }

    public static ModulesResponse ToResponse(this ModulesDataModel data)
    {
        // TODO: Fix this
        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        var response = new ModulesResponse()
        {
            Id = data.Id,
            Name = data.Name,
            Description = data.Description,
            Attributes = JsonSerializer.Deserialize<ModuleAttribute>(data.Attributes, options)!,
            CreatedBy = data.CreatedBy,
            CreatedAt = data.CreatedAt,
            ModifiedBy = data.ModifiedBy,
            ModifiedAt = data.ModifiedAt,
        };
        return response;
    }

    public static GetUsersResponse ToResponse(this List<UserDto> data)
    {
        var response = new GetUsersResponse()
        {
            Users = [.. data.Select(ToResponse)]
        };
        return response;
    }

    private static UserResponse ToResponse(this UserDto data)
    {
        var response = new UserResponse()
        {
            EmailAddress = data.EmailAddress,
            FirstName = data.FirstName,
            LastName = data.LastName,
            Id = data.Id,
            UserStatus = data.UserStatus,
            OrganisationStatus = data.OrganisationStatus,
            IsOwner = data.IsOwner
        };
        return response;
    }

    public static PermissionsResponse ToResponse(this List<PermissionData> data)
    {
        var response = new PermissionsResponse()
        {
            Permissions = [.. data.Select(ToResponse)]
        };

        return response;
    }

    public static PermissionResponse ToResponse(this PermissionData data)
    {
        var response = new PermissionResponse()
        {
            Id = data.Id,
            PermissionDescription = data.PermissionDescription,
            PermissionName = data.PermissionName,
            Scope = data.Scope,
            Domain = data.Domain
        };
        return response;
    }

    public static GetRolesResponse ToResponse(this List<RoleDataModel> data)
    {
        return new GetRolesResponse()
        {
            Roles = [.. data.Select(ToResponse)]
        };
    }

    public static RoleResponse ToResponse(this RoleDataModel data)
    {
        return new RoleResponse()
        {
            Id = data.Id,
            RoleName = data.RoleName,
            RoleDescription = data.RoleDescription,
            OrganisationId = data.OrganisationId,
            CreatedBy = data.CreatedBy,
            CreatedAt = data.CreatedAt,
        };
    }
}
