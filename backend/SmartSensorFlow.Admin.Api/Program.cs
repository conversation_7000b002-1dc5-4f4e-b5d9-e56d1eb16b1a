using System.Text.Json.Serialization;
using System.Threading.Channels;
using Serilog;
using SmartSensorFlow.Admin.Api.Extensions;
using SmartSensorFlow.Admin.Api.Middleware;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Worker;
using SmartSensorFlow.Admin.Worker.Messages;

var builder = WebApplication.CreateBuilder(args);

// Configure logger
Log.Logger = new LoggerConfiguration()
    // add console as logging target
    .WriteTo.Console()
    // set default minimum level
    .MinimumLevel.Debug()
    .CreateLogger();

var configurationBuilder = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json")
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true)
    .AddEnvironmentVariables();
IConfiguration configuration = configurationBuilder.Build();

// Controllers and Swagger
var serviceCollection = builder.Services;
serviceCollection.AddControllers().AddJsonOptions(options =>
{
  options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
  options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
});
serviceCollection.AddCustomCors(configuration);
serviceCollection.AddEndpointsApiExplorer();
serviceCollection.AddCustomProblemDetails();
serviceCollection.AddCustomResponseCompression();
serviceCollection.AddCustomSwagger();
serviceCollection.AddCustomSecurity(configuration);
serviceCollection.AddCustomServices(configuration);
serviceCollection.AddScoped<IRequestContextProvider, RequestContextProvider>();
serviceCollection.AddPaddlePaymentService(configuration);
serviceCollection.AddEmailIntegration(configuration);
serviceCollection.AddInviteConfiguration(configuration);

// Create the channel
var queue = Channel.CreateUnbounded<PaddleWebhookMessage>();
builder.Services.AddSingleton(queue);

// Register the background worker
builder.Services.AddHostedService<PaddleWebhookProcessor>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsProduction())
{
  app.UseSwagger();
  app.UseSwaggerUI();
}

// app.UseHttpsRedirection();
app.UseMiddleware<ContextProviderMiddleware>();
app.UseCustomCors();
app.MapControllers();
app.Run();
