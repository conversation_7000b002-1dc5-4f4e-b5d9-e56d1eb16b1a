using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Billing.SupportProducts.Models;
using SmartSensorFlow.Admin.Api.Extensions;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.SupportProducts;

[Route("api/billing/support-products")]
[ApiController]
public class GetSupportProductsController : ControllerBase
{
    private readonly IQueryHandler _queryHandler;

    public GetSupportProductsController(IQueryHandler queryHandler)
    {
        _queryHandler = queryHandler;
    }

    [HttpGet]
    [ProducesResponseType(typeof(GetSupportProductsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Get()
    {
        var products = await _queryHandler.GetAllSupportProducts();
        return Ok(products.ToResponse());
    }
}
