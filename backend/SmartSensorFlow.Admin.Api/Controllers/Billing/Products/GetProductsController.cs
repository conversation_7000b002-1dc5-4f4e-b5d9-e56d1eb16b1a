using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Products.Models;
using SmartSensorFlow.Admin.Api.Extensions;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Products;

[Route("api/billing/products")]
[ApiController]
public class GetProductsController(IQueryHandler queryHandler) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(GetProductsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Get()
    {
        var products = await queryHandler.GetAllProducts();
        return Ok(products.ToResponse());
    }
}
