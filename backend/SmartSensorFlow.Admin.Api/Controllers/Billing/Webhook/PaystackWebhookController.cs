using System.Security.Cryptography;
using System.Text;
using System.Threading.Channels;
using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Integration.Paystack.Config;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;
using SmartSensorFlow.Admin.Worker.Messages;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Webhook;

[Route("api/billing")]
public class PaystackWebhookController : ControllerBase
{
    private readonly ILogger<PaystackWebhookController> _logger;
    private readonly PaystackPlatformConfiguration _platformConfiguration;
    private readonly PaystackSupportConfiguration _supportConfiguration;
    private readonly Channel<PaystackWebhookMessage> _queue;

    public PaystackWebhookController(
        ILogger<PaystackWebhookController> logger,
        PaystackPlatformConfiguration platformConfiguration,
        PaystackSupportConfiguration supportConfiguration,
        Channel<PaystackWebhookMessage> queue)
    {
        _logger = logger;
        _platformConfiguration = platformConfiguration;
        _supportConfiguration = supportConfiguration;
        _queue = queue;
    }

    [HttpPost("webhook/paystack/platform")]
    public async Task<IActionResult> HandlePlatformWebhook()
    {
        return await HandleWebhook(PaystackSubscriptionType.Platform, _platformConfiguration.SecretKey);
    }

    [HttpPost("webhook/paystack/support")]
    public async Task<IActionResult> HandleSupportWebhook()
    {
        return await HandleWebhook(PaystackSubscriptionType.Support, _supportConfiguration.SecretKey);
    }

    private async Task<IActionResult> HandleWebhook(PaystackSubscriptionType subscriptionType, string secretKey)
    {
        try
        {
            Request.EnableBuffering();

            // Get the Paystack signature header
            if (!Request.Headers.TryGetValue("x-paystack-signature", out var paystackSignature) || string.IsNullOrEmpty(paystackSignature))
            {
                _logger.LogError("x-paystack-signature not present in request headers");
                return BadRequest(new { error = "Invalid request" });
            }

            if (string.IsNullOrEmpty(secretKey))
            {
                _logger.LogError("Secret key not defined for subscription type: {SubscriptionType}", subscriptionType);
                return StatusCode(500, new { error = "Server misconfigured" });
            }

            // Read the raw request body
            using var reader = new StreamReader(Request.Body, Encoding.UTF8);
            var bodyRaw = await reader.ReadToEndAsync();
            Request.Body.Position = 0;

            // Verify the signature
            if (!VerifyPaystackSignature(bodyRaw, paystackSignature!, secretKey))
            {
                _logger.LogError("Computed signature does not match Paystack signature");
                return Unauthorized(new { error = "Invalid signature" });
            }

            // Process the webhook event
            await _queue.Writer.WriteAsync(new PaystackWebhookMessage()
            {
                RawBody = bodyRaw,
                SubscriptionType = subscriptionType
            });

            return Ok(new { success = true });
        }
        catch (Exception ex)
        {
            _logger.LogError("Failed to verify and process Paystack webhook {message}", ex.Message);
            return StatusCode(500, new { error = "Failed to verify and process webhook" });
        }
    }

    private bool VerifyPaystackSignature(string payload, string signature, string secretKey)
    {
        try
        {
            using var hmac = new HMACSHA512(Encoding.UTF8.GetBytes(secretKey));
            var computedHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
            var computedSignature = BitConverter.ToString(computedHash).Replace("-", "").ToLower();

            return string.Equals(computedSignature, signature, StringComparison.OrdinalIgnoreCase);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying Paystack signature");
            return false;
        }
    }
}
