using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Subscription.CancelSubscription.Models;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Integration.Paddle;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Subscription.CancelSubscription;

[Route("api/billing")]
[ApiController]
public class CancelSubscriptionController(IPaddleService paddleService, IQueryHandler queryHandler) : ProblemDetailsControllerBase
{
    [HttpPost("subscription/cancel")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Cancel(CancelSubscriptionRequest request)
    {
        var subscription = await queryHandler.GetSubscriptionBySubscriptionId(request.SubscriptionId);

        if (subscription is null)
        {
            return ValidationProblem("Invalid subscriptionId", []);
        }
        await paddleService.CancelSubscription(subscription.PaddleSubscriptionId, request.CancellationType);
        return Ok();
    }
}
