using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Subscription.GetSubscription.Models;
using SmartSensorFlow.Admin.Api.Extensions;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Subscription.GetSubscription;

[Route("api/billing")]
[ApiController]
public class GetSubscriptionsController(IQueryHandler queryHandler) : ControllerBase
{
    [HttpGet("subscription/{organisationId}")]
    [ProducesResponseType(typeof(GetSubscriptionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Get(int organisationId)
    {
        var subscriptions = await queryHandler.GetSubscriptionByOrganisationId(organisationId);
        var response = subscriptions.ToResponse();
        return Ok(response);
    }
}
