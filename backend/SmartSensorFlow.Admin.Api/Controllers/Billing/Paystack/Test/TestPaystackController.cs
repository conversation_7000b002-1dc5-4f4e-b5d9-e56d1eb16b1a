using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Integration.Paystack;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.Test;

[Route("api/billing/paystack/test")]
[ApiController]
public class TestPaystackController : ControllerBase
{
    private readonly IPaystackService _paystackService;
    private readonly IQueryHandler _queryHandler;

    public TestPaystackController(IPaystackService paystackService, IQueryHandler queryHandler)
    {
        _paystackService = paystackService;
        _queryHandler = queryHandler;
    }

    [HttpGet("verify-platform/{reference}")]
    public async Task<IActionResult> TestVerifyPlatformTransaction(string reference)
    {
        try
        {
            var result = await _paystackService.VerifyTransaction(reference, PaystackSubscriptionType.Platform);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpGet("verify-support/{reference}")]
    public async Task<IActionResult> TestVerifySupportTransaction(string reference)
    {
        try
        {
            var result = await _paystackService.VerifyTransaction(reference, PaystackSubscriptionType.Support);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpGet("test-amount-parsing/{priceString}")]
    public IActionResult TestAmountParsing(string priceString)
    {
        try
        {
            var kobo = ParsePriceToKobo(priceString);
            return Ok(new
            {
                input = priceString,
                kobo = kobo,
                rand = kobo / 100.0,
                message = $"'{priceString}' -> {kobo} kobo (R{kobo / 100.0:F2})"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpPost("create-subscription-manually")]
    public async Task<IActionResult> CreateSubscriptionManually([FromBody] CreateSubscriptionRequest request)
    {
        try
        {
            // Find the user by email
            var user = await _queryHandler.GetUserByEmail(request.Email);
            if (user == null)
            {
                return BadRequest(new { error = "User not found" });
            }

            // Find the user's organizations
            var organizations = await _queryHandler.GetOrganisationsByUserId(user.Id);
            var organization = organizations.FirstOrDefault();
            if (organization == null)
            {
                return BadRequest(new { error = "No organization found for user" });
            }

            // Find the product
            var product = request.SubscriptionType == PaystackSubscriptionType.Platform
                ? await _queryHandler.GetProductByPlanId(request.PlanCode)
                : null;

            var supportProduct = request.SubscriptionType == PaystackSubscriptionType.Support
                ? await _queryHandler.GetSupportProductByPlanId(request.PlanCode)
                : null;

            if (product == null && supportProduct == null)
            {
                return BadRequest(new { error = "Product not found" });
            }

            // Create the subscription
            if (request.SubscriptionType == PaystackSubscriptionType.Platform && product != null)
            {
                await _queryHandler.AddOrUpdatePaystackPlatformSubscription(
                    product.Id,
                    organization.Id,
                    request.SubscriptionCode,
                    request.Amount,
                    request.Currency,
                    DateTime.UtcNow.AddMonths(1));
            }
            else if (request.SubscriptionType == PaystackSubscriptionType.Support && supportProduct != null)
            {
                await _queryHandler.AddOrUpdatePaystackSupportSubscription(
                    supportProduct.Id,
                    organization.Id,
                    request.SubscriptionCode,
                    request.Amount,
                    request.Currency,
                    DateTime.UtcNow.AddMonths(1));
            }

            return Ok(new { message = "Subscription created successfully" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message });
        }
    }

    private int ParsePriceToKobo(string priceString)
    {
        try
        {
            // Remove currency symbol, thousand separators, and any text after the number
            var numericPart = priceString
                .Replace("R", "")
                .Replace(",", "")
                .Split('/')[0]
                .Trim();

            // Parse as decimal
            if (decimal.TryParse(numericPart, out decimal price))
            {
                // Convert to kobo (multiply by 100 to get cents)
                return (int)(price * 100);
            }

            // Default fallback if parsing fails
            return 100000; // 1000.00 in kobo
        }
        catch
        {
            // Default fallback if any exception occurs
            return 100000; // 1000.00 in kobo
        }
    }
}

public class CreateSubscriptionRequest
{
    public required string Email { get; set; }
    public required string PlanCode { get; set; }
    public required string SubscriptionCode { get; set; }
    public required long Amount { get; set; }
    public required string Currency { get; set; }
    public required PaystackSubscriptionType SubscriptionType { get; set; }
}