using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Integration.Paystack;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.Test;

[Route("api/billing/paystack/test")]
[ApiController]
public class TestPaystackController : ControllerBase
{
    private readonly IPaystackService _paystackService;

    public TestPaystackController(IPaystackService paystackService)
    {
        _paystackService = paystackService;
    }

    [HttpGet("verify-platform/{reference}")]
    public async Task<IActionResult> TestVerifyPlatformTransaction(string reference)
    {
        try
        {
            var result = await _paystackService.VerifyTransaction(reference, PaystackSubscriptionType.Platform);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpGet("verify-support/{reference}")]
    public async Task<IActionResult> TestVerifySupportTransaction(string reference)
    {
        try
        {
            var result = await _paystackService.VerifyTransaction(reference, PaystackSubscriptionType.Support);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message });
        }
    }
}
