using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.VerifyTransaction.Models;
using SmartSensorFlow.Admin.Integration.Paystack;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.VerifyTransaction;

[Route("api/billing/paystack")]
[ApiController]
public class VerifyTransactionController : ProblemDetailsControllerBase
{
    private readonly IPaystackService _paystackService;

    public VerifyTransactionController(IPaystackService paystackService)
    {
        _paystackService = paystackService;
    }

    [HttpGet("verify/platform")]
    [ProducesResponseType(typeof(VerifyTransactionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> VerifyPlatformTransaction([FromQuery] string reference)
    {
        if (string.IsNullOrEmpty(reference))
        {
            return ValidationProblem("Reference is required", []);
        }

        var result = await _paystackService.VerifyTransaction(reference, PaystackSubscriptionType.Platform);

        if (!result.Status || result.Data == null)
        {
            return ValidationProblem(result.Message, []);
        }

        return Ok(new VerifyTransactionResponse
        {
            Status = result.Data.Status,
            Reference = result.Data.Reference,
            Amount = result.Data.Amount,
            Currency = result.Data.Currency,
            CustomerEmail = result.Data.Customer?.Email ?? string.Empty,
            PlanCode = result.Data.Plan?.PlanCode ?? string.Empty,
            SubscriptionCode = result.Data.Subscription?.SubscriptionCode ?? string.Empty
        });
    }

    [HttpGet("verify/support")]
    [ProducesResponseType(typeof(VerifyTransactionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> VerifySupportTransaction([FromQuery] string reference)
    {
        if (string.IsNullOrEmpty(reference))
        {
            return ValidationProblem("Reference is required", []);
        }

        var result = await _paystackService.VerifyTransaction(reference, PaystackSubscriptionType.Support);

        if (!result.Status || result.Data == null)
        {
            return ValidationProblem(result.Message, []);
        }

        return Ok(new VerifyTransactionResponse
        {
            Status = result.Data.Status,
            Reference = result.Data.Reference,
            Amount = result.Data.Amount,
            Currency = result.Data.Currency,
            CustomerEmail = result.Data.Customer?.Email ?? string.Empty,
            PlanCode = result.Data.Plan?.PlanCode ?? string.Empty,
            SubscriptionCode = result.Data.Subscription?.SubscriptionCode ?? string.Empty
        });
    }
}
