using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.VerifyTransaction.Models;
using SmartSensorFlow.Admin.Integration.Paystack;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.VerifyTransaction;

[Route("api/billing/paystack")]
[ApiController]
public class VerifyTransactionController : ProblemDetailsControllerBase
{
    private readonly IPaystackService _paystackService;

    public VerifyTransactionController(IPaystackService paystackService)
    {
        _paystackService = paystackService;
    }

    [HttpGet("verify/platform")]
    [ProducesResponseType(typeof(VerifyTransactionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> VerifyPlatformTransaction([FromQuery] string reference)
    {
        if (string.IsNullOrEmpty(reference))
        {
            return ValidationProblem("Reference is required", []);
        }

        var result = await _paystackService.VerifyTransaction(reference, PaystackSubscriptionType.Platform);

        if (!result.Status || result.Data == null)
        {
            return ValidationProblem(result.Message, []);
        }

        // Extract plan code from the plan object or string
        string planCode = ExtractPlanCode(result.Data.Plan);

        return Ok(new VerifyTransactionResponse
        {
            Status = result.Data.Status,
            Reference = result.Data.Reference,
            Amount = result.Data.Amount,
            Currency = result.Data.Currency,
            CustomerEmail = result.Data.Customer?.Email ?? string.Empty,
            PlanCode = planCode,
            SubscriptionCode = result.Data.Subscription?.SubscriptionCode ?? string.Empty
        });
    }

    [HttpGet("verify/support")]
    [ProducesResponseType(typeof(VerifyTransactionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> VerifySupportTransaction([FromQuery] string reference)
    {
        if (string.IsNullOrEmpty(reference))
        {
            return ValidationProblem("Reference is required", []);
        }

        var result = await _paystackService.VerifyTransaction(reference, PaystackSubscriptionType.Support);

        if (!result.Status || result.Data == null)
        {
            return ValidationProblem(result.Message, []);
        }

        // Extract plan code from the plan object or string
        string planCode = ExtractPlanCode(result.Data.Plan);

        return Ok(new VerifyTransactionResponse
        {
            Status = result.Data.Status,
            Reference = result.Data.Reference,
            Amount = result.Data.Amount,
            Currency = result.Data.Currency,
            CustomerEmail = result.Data.Customer?.Email ?? string.Empty,
            PlanCode = planCode,
            SubscriptionCode = result.Data.Subscription?.SubscriptionCode ?? string.Empty
        });
    }

    /// <summary>
    /// Extracts the plan code from the plan object, handling both string and object formats
    /// </summary>
    /// <param name="plan">The plan object from Paystack response</param>
    /// <returns>The plan code string</returns>
    private string ExtractPlanCode(object? plan)
    {
        if (plan == null)
            return string.Empty;

        try
        {
            // If it's already a string, return it
            if (plan is string planString)
                return planString;

            // If it's a JsonElement, try to extract the plan_code
            if (plan is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.String)
                    return jsonElement.GetString() ?? string.Empty;

                if (jsonElement.ValueKind == JsonValueKind.Object && jsonElement.TryGetProperty("plan_code", out var planCodeProperty))
                    return planCodeProperty.GetString() ?? string.Empty;
            }

            // Try to serialize and deserialize as PaystackPlan
            var jsonString = JsonSerializer.Serialize(plan);
            var planObject = JsonSerializer.Deserialize<PaystackPlan>(jsonString);
            return planObject?.PlanCode ?? string.Empty;
        }
        catch
        {
            // If all else fails, return empty string
            return string.Empty;
        }
    }
}

public class PaystackPlan
{
    [JsonPropertyName("plan_code")]
    public string PlanCode { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;
}
