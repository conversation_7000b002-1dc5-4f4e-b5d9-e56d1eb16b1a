using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.InitializeTransaction.Models;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Integration.Paystack;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.InitializeTransaction;

[Route("api/billing/paystack")]
[ApiController]
public class InitializeTransactionController : ProblemDetailsControllerBase
{
    private readonly IPaystackService _paystackService;
    private readonly IQueryHandler _queryHandler;

    public InitializeTransactionController(IPaystackService paystackService, IQueryHandler queryHandler)
    {
        _paystackService = paystackService;
        _queryHandler = queryHandler;
    }

    [HttpPost("initialize/platform")]
    [ProducesResponseType(typeof(InitializeTransactionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> InitializePlatformTransaction([FromBody] InitializeTransactionRequest request)
    {
        // Validate request
        if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.PlanCode))
        {
            return ValidationProblem("Email and PlanCode are required", []);
        }

        // Verify product exists
        Console.WriteLine($"Platform payment: Looking for product with plan code: {request.PlanCode}");
        var product = await _queryHandler.GetProductByPlanId(request.PlanCode);
        if (product == null)
        {
            Console.WriteLine($"Platform payment: Product not found for plan code: {request.PlanCode}");
            return ValidationProblem("Invalid product plan code", []);
        }
        Console.WriteLine($"Platform payment: Found product: {product.Name}");

        // Parse the price from the product (e.g., "R1400.00/month" -> 140000 kobo)
        var priceAmount = ParsePriceToKobo(product.Price);
        Console.WriteLine($"Platform payment: Product price '{product.Price}' converted to {priceAmount} kobo");

        // Initialize transaction
        var metadata = new
        {
            organization_id = request.OrganizationId,
            product_id = product.Id,
            user_email = request.Email
        };

        var result = await _paystackService.InitializeTransaction(
            request.Email,
            request.PlanCode,
            PaystackSubscriptionType.Platform,
            priceAmount,
            metadata);

        if (!result.Status || result.Data == null)
        {
            return ValidationProblem(result.Message, []);
        }

        return Ok(new InitializeTransactionResponse
        {
            AuthorizationUrl = result.Data.AuthorizationUrl,
            Reference = result.Data.Reference
        });
    }

    [HttpPost("initialize/support")]
    [ProducesResponseType(typeof(InitializeTransactionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> InitializeSupportTransaction([FromBody] InitializeTransactionRequest request)
    {
        // Validate request
        if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.PlanCode))
        {
            return ValidationProblem("Email and PlanCode are required", []);
        }

        // Verify support product exists
        Console.WriteLine($"Support payment: Looking for support product with plan code: {request.PlanCode}");
        var supportProduct = await _queryHandler.GetSupportProductByPlanId(request.PlanCode);
        if (supportProduct == null)
        {
            Console.WriteLine($"Support payment: Support product not found for plan code: {request.PlanCode}");
            return ValidationProblem("Invalid support product plan code", []);
        }
        Console.WriteLine($"Support payment: Found support product: {supportProduct.Name}");

        // Parse the price from the support product (e.g., "R1400.00/month" -> 140000 kobo)
        var priceAmount = ParsePriceToKobo(supportProduct.Price);
        Console.WriteLine($"Support payment: Product price '{supportProduct.Price}' converted to {priceAmount} kobo");

        // Initialize transaction
        var metadata = new
        {
            organization_id = request.OrganizationId,
            support_product_id = supportProduct.Id,
            user_email = request.Email
        };

        var result = await _paystackService.InitializeTransaction(
            request.Email,
            request.PlanCode,
            PaystackSubscriptionType.Support,
            priceAmount,
            metadata);

        if (!result.Status || result.Data == null)
        {
            return ValidationProblem(result.Message, []);
        }

        return Ok(new InitializeTransactionResponse
        {
            AuthorizationUrl = result.Data.AuthorizationUrl,
            Reference = result.Data.Reference
        });
    }

    /// <summary>
    /// Parses a price string like "R1400.00/month" to kobo (cents) for Paystack
    /// </summary>
    /// <param name="priceString">The price string from the database</param>
    /// <returns>The price in kobo (cents)</returns>
    private int ParsePriceToKobo(string priceString)
    {
        try
        {
            Console.WriteLine($"ParsePriceToKobo: Input string = '{priceString}'");

            // Remove currency symbol, thousand separators, and any text after the number
            var numericPart = priceString
                .Replace("R", "")
                .Replace(",", "")
                .Split('/')[0]
                .Trim();

            Console.WriteLine($"ParsePriceToKobo: Numeric part = '{numericPart}'");

            // Parse as decimal
            if (decimal.TryParse(numericPart, out decimal price))
            {
                // Convert to kobo (multiply by 100 to get cents)
                var kobo = (int)(price * 100);
                Console.WriteLine($"ParsePriceToKobo: Parsed price = {price}, kobo = {kobo}");
                return kobo;
            }

            Console.WriteLine("ParsePriceToKobo: Failed to parse, using fallback");
            // Default fallback if parsing fails
            return 100000; // 1000.00 in kobo
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ParsePriceToKobo: Exception = {ex.Message}");
            // Default fallback if any exception occurs
            return 100000; // 1000.00 in kobo
        }
    }
}
