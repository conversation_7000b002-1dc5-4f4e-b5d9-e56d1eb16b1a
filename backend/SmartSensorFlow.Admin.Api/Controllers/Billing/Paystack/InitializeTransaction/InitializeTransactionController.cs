using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.InitializeTransaction.Models;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Integration.Paystack;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.InitializeTransaction;

[Route("api/billing/paystack")]
[ApiController]
public class InitializeTransactionController : ProblemDetailsControllerBase
{
    private readonly IPaystackService _paystackService;
    private readonly IQueryHandler _queryHandler;

    public InitializeTransactionController(IPaystackService paystackService, IQueryHandler queryHandler)
    {
        _paystackService = paystackService;
        _queryHandler = queryHandler;
    }

    [HttpPost("initialize/platform")]
    [ProducesResponseType(typeof(InitializeTransactionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> InitializePlatformTransaction([FromBody] InitializeTransactionRequest request)
    {
        // Validate request
        if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.PlanCode))
        {
            return ValidationProblem("Email and PlanCode are required", []);
        }

        // Verify product exists
        var product = await _queryHandler.GetProductByPlanId(request.PlanCode);
        if (product == null)
        {
            return ValidationProblem("Invalid product plan code", []);
        }

        // Initialize transaction
        var metadata = new
        {
            organization_id = request.OrganizationId,
            product_id = product.Id,
            user_email = request.Email
        };

        var result = await _paystackService.InitializeTransaction(
            request.Email,
            request.PlanCode,
            PaystackSubscriptionType.Platform,
            metadata);

        if (!result.Status || result.Data == null)
        {
            return ValidationProblem(result.Message, []);
        }

        return Ok(new InitializeTransactionResponse
        {
            AuthorizationUrl = result.Data.AuthorizationUrl,
            Reference = result.Data.Reference
        });
    }

    [HttpPost("initialize/support")]
    [ProducesResponseType(typeof(InitializeTransactionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> InitializeSupportTransaction([FromBody] InitializeTransactionRequest request)
    {
        // Validate request
        if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.PlanCode))
        {
            return ValidationProblem("Email and PlanCode are required", []);
        }

        // Verify support product exists
        var supportProduct = await _queryHandler.GetSupportProductByPlanId(request.PlanCode);
        if (supportProduct == null)
        {
            return ValidationProblem("Invalid support product plan code", []);
        }

        // Initialize transaction
        var metadata = new
        {
            organization_id = request.OrganizationId,
            support_product_id = supportProduct.Id,
            user_email = request.Email
        };

        var result = await _paystackService.InitializeTransaction(
            request.Email,
            request.PlanCode,
            PaystackSubscriptionType.Support,
            metadata);

        if (!result.Status || result.Data == null)
        {
            return ValidationProblem(result.Message, []);
        }

        return Ok(new InitializeTransactionResponse
        {
            AuthorizationUrl = result.Data.AuthorizationUrl,
            Reference = result.Data.Reference
        });
    }
}
