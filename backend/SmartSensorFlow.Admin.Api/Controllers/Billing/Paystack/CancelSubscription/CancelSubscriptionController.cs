using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.CancelSubscription.Models;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Integration.Paystack;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Paystack.CancelSubscription;

[Route("api/billing/paystack")]
[ApiController]
public class CancelSubscriptionController : ProblemDetailsControllerBase
{
    private readonly IPaystackService _paystackService;
    private readonly IQueryHandler _queryHandler;

    public CancelSubscriptionController(IPaystackService paystackService, IQueryHandler queryHandler)
    {
        _paystackService = paystackService;
        _queryHandler = queryHandler;
    }

    [HttpPost("cancel/platform")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CancelPlatformSubscription([FromBody] PaystackCancelSubscriptionRequest request)
    {
        if (string.IsNullOrEmpty(request.SubscriptionCode) || string.IsNullOrEmpty(request.EmailToken))
        {
            return ValidationProblem("SubscriptionCode and EmailToken are required", []);
        }

        var result = await _paystackService.CancelSubscription(
            request.SubscriptionCode,
            request.EmailToken,
            PaystackSubscriptionType.Platform);

        if (!result)
        {
            return ValidationProblem("Failed to cancel subscription", []);
        }

        // Update subscription status in database
        await _queryHandler.CancelPaystackSubscription(request.SubscriptionCode, PaystackSubscriptionType.Platform);

        return Ok();
    }

    [HttpPost("cancel/support")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CancelSupportSubscription([FromBody] PaystackCancelSubscriptionRequest request)
    {
        if (string.IsNullOrEmpty(request.SubscriptionCode) || string.IsNullOrEmpty(request.EmailToken))
        {
            return ValidationProblem("SubscriptionCode and EmailToken are required", []);
        }

        var result = await _paystackService.CancelSubscription(
            request.SubscriptionCode,
            request.EmailToken,
            PaystackSubscriptionType.Support);

        if (!result)
        {
            return ValidationProblem("Failed to cancel subscription", []);
        }

        // Update subscription status in database
        await _queryHandler.CancelPaystackSubscription(request.SubscriptionCode, PaystackSubscriptionType.Support);

        return Ok();
    }
}
