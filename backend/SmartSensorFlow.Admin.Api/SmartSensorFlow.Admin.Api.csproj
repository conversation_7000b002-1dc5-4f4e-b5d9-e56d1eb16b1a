<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.GuardClauses" Version="5.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.6" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../SmartSensorFlow.Admin.Common/SmartSensorFlow.Admin.Common.csproj" />
    <ProjectReference Include="../SmartSensorFlow.Admin.Data/SmartSensorFlow.Admin.Data.csproj" />
    <ProjectReference Include="../SmartSensorFlow.Admin.Integration.EmailJS/SmartSensorFlow.Admin.Integration.EmailJS.csproj" />
    <ProjectReference Include="../SmartSensorFlow.Admin.Integration.Paddle/SmartSensorFlow.Admin.Integration.Paddle.csproj" />
    <ProjectReference Include="../SmartSensorFlow.Admin.Security/SmartSensorFlow.Admin.Security.csproj" />
    <ProjectReference Include="../SmartSensorFlow.Admin.Worker/SmartSensorFlow.Admin.Worker.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers/Billing/Payments/" />
  </ItemGroup>

</Project>
