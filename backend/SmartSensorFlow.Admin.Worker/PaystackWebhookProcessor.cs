using System.Threading.Channels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Integration.Paystack.Enums;
using SmartSensorFlow.Admin.Worker.Messages;

namespace SmartSensorFlow.Admin.Worker;

public class PaystackWebhookProcessor : IHostedService
{
    private readonly Channel<PaystackWebhookMessage> _queue;
    private readonly ILogger<PaystackWebhookProcessor> _logger;
    private readonly IServiceProvider _services;
    private Task? _workerTask;
    private CancellationTokenSource _cts = new();

    public PaystackWebhookProcessor(
        Channel<PaystackWebhookMessage> queue,
        ILogger<PaystackWebhookProcessor> logger,
        IServiceProvider services)
    {
        _queue = queue;
        _logger = logger;
        _services = services;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _workerTask = Task.Run(() => RunAsync(_cts.Token), cancellationToken);
        return Task.CompletedTask;
    }

    public async Task RunAsync(CancellationToken cancellationToken)
    {
        try
        {
            await foreach (var message in _queue.Reader.ReadAllAsync(cancellationToken))
            {
                var responseData = JObject.Parse(message.RawBody);
                var webhookEvent = responseData["event"]?.ToString();

                if (webhookEvent is not null)
                {
                    switch (webhookEvent)
                    {
                        case "charge.success":
                            await ProcessChargeSuccess(responseData, message.SubscriptionType);
                            break;
                        case "subscription.create":
                            await ProcessSubscriptionCreated(responseData, message.SubscriptionType);
                            break;
                        case "subscription.disable":
                            await ProcessSubscriptionDisabled(responseData, message.SubscriptionType);
                            break;
                        case "invoice.create":
                            await ProcessInvoiceCreated(responseData, message.SubscriptionType);
                            break;
                        case "invoice.payment_failed":
                            await ProcessInvoicePaymentFailed(responseData, message.SubscriptionType);
                            break;
                        default:
                            _logger.LogInformation("Unhandled Paystack webhook event: {Event}", webhookEvent);
                            break;
                    }
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogError("Exception thrown in Paystack webhook processor: {e}", e.Message);
        }
    }

    private async Task ProcessChargeSuccess(JObject responseData, PaystackSubscriptionType subscriptionType)
    {
        using var scope = _services.CreateScope();
        var queryHandler = scope.ServiceProvider.GetRequiredService<IQueryHandler>();

        try
        {
            var chargeData = responseData["data"];
            var reference = chargeData?["reference"]?.ToString();
            var customerEmail = chargeData?["customer"]?["email"]?.ToString();
            var amount = chargeData?["amount"]?.Value<long>();
            var currency = chargeData?["currency"]?.ToString();
            var status = chargeData?["status"]?.ToString();
            var planCode = chargeData?["plan"]?["plan_code"]?.ToString();

            if (string.IsNullOrEmpty(reference) || string.IsNullOrEmpty(customerEmail) ||
                !amount.HasValue || status != "success")
            {
                _logger.LogError("Missing required charge data in webhook or charge not successful");
                return;
            }

            _logger.LogInformation("Processing successful charge: {Reference} for {Email} with plan {PlanCode}",
                reference, customerEmail, planCode);

            // For now, we'll log the successful charge and let the subscription.create event handle the actual subscription creation
            // This is because Paystack typically sends charge.success first, then subscription.create for subscription payments

            _logger.LogInformation("Charge successful for {SubscriptionType}: Reference={Reference}, Amount={Amount}, Customer={Email}",
                subscriptionType, reference, amount, customerEmail);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing charge success webhook");
        }
    }

    private async Task ProcessSubscriptionCreated(JObject responseData, PaystackSubscriptionType subscriptionType)
    {
        using var scope = _services.CreateScope();
        var queryHandler = scope.ServiceProvider.GetRequiredService<IQueryHandler>();

        try
        {
            var subscriptionData = responseData["data"];
            var subscriptionCode = subscriptionData?["subscription_code"]?.ToString();
            var customerEmail = subscriptionData?["customer"]?["email"]?.ToString();
            var planCode = subscriptionData?["plan"]?["plan_code"]?.ToString();
            var amount = subscriptionData?["amount"]?.Value<long>();
            var currency = subscriptionData?["currency"]?.ToString();
            var nextPaymentDate = subscriptionData?["next_payment_date"]?.ToString();

            _logger.LogInformation("Processing subscription created: {SubscriptionCode} for {Email} with plan {PlanCode}",
                subscriptionCode, customerEmail, planCode);

            if (string.IsNullOrEmpty(subscriptionCode) || string.IsNullOrEmpty(customerEmail) ||
                string.IsNullOrEmpty(planCode) || !amount.HasValue)
            {
                _logger.LogError("Missing required subscription data in webhook");
                return;
            }

            // Find the organization and product based on the plan code and customer email
            var product = subscriptionType == PaystackSubscriptionType.Platform
                ? await queryHandler.GetProductByPlanId(planCode)
                : null;

            var supportProduct = subscriptionType == PaystackSubscriptionType.Support
                ? await queryHandler.GetSupportProductByPlanId(planCode)
                : null;

            _logger.LogInformation("Product lookup: Platform product = {Product}, Support product = {SupportProduct}",
                product?.Name ?? "null", supportProduct?.Name ?? "null");

            if (product == null && supportProduct == null)
            {
                _logger.LogError("Product not found for plan code: {PlanCode}", planCode);
                return;
            }

            // Find organization by customer email (assuming the user who created the subscription)
            var user = await queryHandler.GetUserByEmail(customerEmail);
            if (user == null)
            {
                _logger.LogError("User not found for email: {Email}", customerEmail);
                return;
            }

            _logger.LogInformation("Found user: {UserId} ({FirstName} {LastName}) for email: {Email}",
                user.Id, user.FirstName, user.LastName, user.EmailAddress);

            var organizations = await queryHandler.GetOrganisationsByUserId(user.Id);
            _logger.LogInformation("Found {Count} organizations for user {UserId}", organizations.Count, user.Id);

            var organization = organizations.FirstOrDefault(); // For now, use the first organization

            if (organization == null)
            {
                _logger.LogError("No organization found for user: {UserId}", user.Id);
                return;
            }

            _logger.LogInformation("Using organization: {OrgId} ({OrgName})", organization.Id, organization.Name);

            // Check if subscription already exists
            var existingSubscription = subscriptionType == PaystackSubscriptionType.Platform
                ? await queryHandler.GetSubscriptionByPaystackPlatformSubscriptionId(subscriptionCode)
                : await queryHandler.GetSubscriptionByPaystackSupportSubscriptionId(subscriptionCode);

            if (existingSubscription != null)
            {
                _logger.LogWarning("Subscription already exists: {SubscriptionCode}", subscriptionCode);
                return;
            }

            // Create or update subscription
            var nextPayment = DateTime.TryParse(nextPaymentDate, out var parsedDate)
                ? parsedDate.ToUniversalTime()
                : DateTime.UtcNow.AddMonths(1);

            if (subscriptionType == PaystackSubscriptionType.Platform)
            {
                await queryHandler.AddOrUpdatePaystackPlatformSubscription(
                    product!.Id,
                    organization.Id,
                    subscriptionCode,
                    amount.Value,
                    currency ?? "ZAR",
                    nextPayment);
            }
            else
            {
                await queryHandler.AddOrUpdatePaystackSupportSubscription(
                    supportProduct!.Id,
                    organization.Id,
                    subscriptionCode,
                    amount.Value,
                    currency ?? "ZAR",
                    nextPayment);
            }

            _logger.LogInformation("Successfully processed subscription created: {SubscriptionCode} for {SubscriptionType}",
                subscriptionCode, subscriptionType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing subscription created webhook");
        }
    }

    private async Task ProcessSubscriptionDisabled(JObject responseData, PaystackSubscriptionType subscriptionType)
    {
        using var scope = _services.CreateScope();
        var queryHandler = scope.ServiceProvider.GetRequiredService<IQueryHandler>();

        try
        {
            var subscriptionData = responseData["data"];
            var subscriptionCode = subscriptionData?["subscription_code"]?.ToString();

            if (string.IsNullOrEmpty(subscriptionCode))
            {
                _logger.LogError("Missing subscription code in disable webhook");
                return;
            }

            // Update subscription status to cancelled
            await queryHandler.CancelPaystackSubscription(subscriptionCode, subscriptionType);

            _logger.LogInformation("Successfully processed subscription disabled: {SubscriptionCode} for {SubscriptionType}",
                subscriptionCode, subscriptionType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing subscription disabled webhook");
        }
    }

    private async Task ProcessInvoiceCreated(JObject responseData, PaystackSubscriptionType subscriptionType)
    {
        _logger.LogInformation("Invoice created for {SubscriptionType}: {Data}", subscriptionType, responseData);
        // Implement invoice processing logic if needed
    }

    private async Task ProcessInvoicePaymentFailed(JObject responseData, PaystackSubscriptionType subscriptionType)
    {
        _logger.LogWarning("Invoice payment failed for {SubscriptionType}: {Data}", subscriptionType, responseData);
        // Implement payment failure handling logic if needed
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _cts.Cancel();
        return _workerTask ?? Task.CompletedTask;
    }
}
