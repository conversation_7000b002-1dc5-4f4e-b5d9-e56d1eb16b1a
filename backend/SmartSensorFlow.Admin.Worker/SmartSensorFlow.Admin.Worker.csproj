<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../SmartSensorFlow.Admin.Data/SmartSensorFlow.Admin.Data.csproj" />
    <ProjectReference Include="../SmartSensorFlow.Admin.Integration.Paystack/SmartSensorFlow.Admin.Integration.Paystack.csproj" />
  </ItemGroup>

</Project>
