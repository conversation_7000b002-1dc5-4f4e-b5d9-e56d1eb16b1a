## Build results
bin/
obj/
out/

## User-specific files
*.user
*.userosscache
*.suo
*.sln.docstates

## Visual Studio settings
.vscode/
.vs/

## ASP.NET Scaffolding
ScaffoldingReadMe.txt

## JetBrains Rider settings
.idea/

## .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# NuGet
*.nupkg
*.snupkg
.nuget/
packages/
*.nuspec
*.psmdcp
*.cache

# Others
*.log
*.tlog
*.tmp
*.bak
*.swp
*.DS_Store
Thumbs.db

# Environment files
.env
*.env.*

# Web
wwwroot/node_modules/
node_modules/

# Test results
TestResults/
*.TestResults/

# Azure
.publish/
*.azurePubxml


# Node modules
node_modules/

# Vite build output
dist/

# Optional: if using Vite preview cache
.vite/

# Local environment files
.env
.env.*
!.env.example

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# OS files
.DS_Store
Thumbs.db

# Dependency directories
.pnpm/
.cache/
.eslintcache
*.tsbuildinfo

# Testing
coverage/
*.log

# Misc
*.local
*.swp
*.bak

# MacOS
.DS_Store

/smartsensorflow

# C# publish
publish
app.zip

# SQL Schema
sqlSchema.db.json